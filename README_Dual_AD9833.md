# 双AD9833信号发生器使用说明

## 项目概述

本项目基于STM32F429挑战者开发板，实现了同时控制两个AD9833信号发生器的功能。通过SysTick系统定时器提供精确延时，可以独立控制两个AD9833模块输出不同频率和波形的信号。

## 硬件连接

### AD9833_1 (原有模块)
```
STM32F429    →    AD9833_1
PA3          →    FSYNC
PA4          →    SCLK  
PA5          →    SDATA
VCC          →    3.3V
GND          →    GND
```

### AD9833_2 (新增模块)
```
STM32F429    →    AD9833_2
PF6          →    FSYNC
PA4          →    SCLK (共享第一个AD9833的SCLK)
PF8          →    SDATA
VCC          →    3.3V
GND          →    GND
```

### 注意事项
- 两个AD9833模块共享电源（VCC和GND）
- SPI信号线完全独立，互不干扰
- 确保连接牢固，避免信号干扰

## 软件架构

### 文件结构
```
User/
├── AD9833/              # 第一个AD9833驱动
│   ├── AD9833.c
│   └── AD9833.h
├── AD9833_2/            # 第二个AD9833驱动
│   ├── AD9833_2.c
│   └── AD9833_2.h
├── led/                 # LED驱动
├── systick/             # SysTick定时器
└── main.c               # 主程序
```

### API对比

| 功能 | AD9833_1 | AD9833_2 |
|------|----------|----------|
| 初始化 | `AD9833_Init()` | `AD9833_2_Init()` |
| 快速设置频率 | `AD9833_SetFrequencyQuick()` | `AD9833_2_SetFrequencyQuick()` |
| 设置频率 | `AD9833_SetFrequency()` | `AD9833_2_SetFrequency()` |
| 设置相位 | `AD9833_SetPhase()` | `AD9833_2_SetPhase()` |
| 复位 | `AD9833_Reset()` | `AD9833_2_Reset()` |

## 使用方法

### 1. 工程配置
在Keil MDK中添加以下文件到编译列表：
- `User/AD9833_2/AD9833_2.c`

### 2. 头文件包含
```c
#include "AD9833.h"      // 第一个AD9833
#include "AD9833_2.h"    // 第二个AD9833
```

### 3. 基本使用示例
```c
int main(void)
{
    // 系统初始化
    SysTick_Init();
    Delay_ms(500);

    // 初始化两个AD9833
    AD9833_Init();        // 初始化第一个AD9833
    AD9833_2_Init();      // 初始化第二个AD9833

    // 设置第一个AD9833输出100kHz正弦波
    AD9833_SetFrequencyQuick(100000.0, AD9833_OUT_SINUS);

    // 设置第二个AD9833输出50kHz三角波
    AD9833_2_SetFrequencyQuick(50000.0, AD9833_2_OUT_TRIANGLE);

    while(1)
    {
        // 主循环
    }
}
```

### 4. 同步输出示例（解决相位漂移问题）
```c
// 同步函数：解决同频率时的相位漂移问题
void dual_ad9833_sync_output(float freq, unsigned short type1, unsigned short type2)
{
    // 1. 同时复位两个AD9833
    AD9833_Reset();
    AD9833_2_Reset();
    Delay_us(10);  // 确保复位完成

    // 2. 配置频率和波形（此时输出仍被复位位抑制）
    AD9833_SetFrequency(AD9833_REG_FREQ0, freq, type1);
    AD9833_2_SetFrequency(AD9833_2_REG_FREQ0, freq, type2);
    Delay_us(10);  // 确保配置完成

    // 3. 同时清除复位位，启动同步输出
    AD9833_ClearReset();
    AD9833_2_ClearReset();
}

int main(void)
{
    // 系统初始化
    SysTick_Init();
    Delay_ms(500);

    // 初始化两个AD9833
    AD9833_Init();
    AD9833_2_Init();

    // 同步输出相同频率的信号（解决相位漂移）
    dual_ad9833_sync_output(100000.0, AD9833_OUT_SINUS, AD9833_2_OUT_SINUS);

    while(1)
    {
        // 示例：每5秒切换一次频率，保持同步
        Delay_ms(5000);
        dual_ad9833_sync_output(50000.0, AD9833_OUT_TRIANGLE, AD9833_2_OUT_TRIANGLE);

        Delay_ms(5000);
        dual_ad9833_sync_output(200000.0, AD9833_OUT_SINUS, AD9833_2_OUT_SINUS);

        Delay_ms(5000);
        dual_ad9833_sync_output(150000.0, AD9833_OUT_MSB, AD9833_2_OUT_MSB);
    }
}
```

### 5. 独立控制示例（不同频率无相位问题）
```c
int main(void)
{
    // 系统初始化
    SysTick_Init();
    Delay_ms(500);

    // 初始化两个AD9833
    AD9833_Init();
    AD9833_2_Init();

    // 不同频率时可以独立设置（无相位漂移问题）
    AD9833_SetFrequencyQuick(100000.0, AD9833_OUT_SINUS);      // 100kHz正弦波
    AD9833_2_SetFrequencyQuick(50000.0, AD9833_2_OUT_TRIANGLE); // 50kHz三角波

    while(1)
    {
        // 动态改变第一个AD9833的波形
        AD9833_SetFrequencyQuick(100000.0, AD9833_OUT_SINUS);
        Delay_ms(2000);

        AD9833_SetFrequencyQuick(100000.0, AD9833_OUT_TRIANGLE);
        Delay_ms(2000);

        AD9833_SetFrequencyQuick(100000.0, AD9833_OUT_MSB);
        Delay_ms(2000);

        // 第二个AD9833保持50kHz三角波不变
    }
}
```

### 6. 扫频应用示例
```c
int main(void)
{
    // 系统初始化
    SysTick_Init();
    Delay_ms(500);

    // 初始化两个AD9833
    AD9833_Init();
    AD9833_2_Init();

    // 第二个AD9833输出固定参考信号
    AD9833_2_SetFrequencyQuick(10000.0, AD9833_2_OUT_SINUS);  // 10kHz参考信号

    while(1)
    {
        // 第一个AD9833进行扫频：1kHz到20kHz
        for(float freq = 1000.0; freq <= 20000.0; freq += 500.0)
        {
            AD9833_SetFrequencyQuick(freq, AD9833_OUT_SINUS);
            Delay_ms(200);  // 每200ms改变一次频率
        }

        // 反向扫频
        for(float freq = 20000.0; freq >= 1000.0; freq -= 500.0)
        {
            AD9833_SetFrequencyQuick(freq, AD9833_OUT_SINUS);
            Delay_ms(200);
        }
    }
}
```

### 7. 波形类型
```c
// AD9833_1 波形类型
AD9833_OUT_SINUS      // 正弦波
AD9833_OUT_TRIANGLE   // 三角波
AD9833_OUT_MSB        // 方波

// AD9833_2 波形类型
AD9833_2_OUT_SINUS    // 正弦波
AD9833_2_OUT_TRIANGLE // 三角波
AD9833_2_OUT_MSB      // 方波
```

### 5. 动态控制示例
```c
while(1)
{
    // 每2秒切换第二个AD9833的波形
    AD9833_2_SetFrequencyQuick(50000.0, AD9833_2_OUT_SINUS);
    Delay_ms(2000);
    
    AD9833_2_SetFrequencyQuick(50000.0, AD9833_2_OUT_TRIANGLE);
    Delay_ms(2000);
    
    AD9833_2_SetFrequencyQuick(50000.0, AD9833_2_OUT_MSB);
    Delay_ms(2000);
}
```

## 技术参数

### 频率范围
- 参考时钟：25MHz
- 频率分辨率：0.1Hz
- 输出频率范围：0.1Hz ~ 12.5MHz

### 波形特性
- 正弦波：低失真，适合模拟信号测试
- 三角波：线性变化，适合扫频应用
- 方波：数字信号，适合时钟信号

## 常见问题

### Q1: 两个AD9833可以输出相同频率吗？
A: 可以。两个模块完全独立，可以输出相同或不同的频率和波形。

### Q2: 如何确保信号同步？
A: 两个AD9833共享相同的参考时钟源，在软件中同时设置可以实现基本同步。

### Q3: 最大输出频率是多少？
A: 理论最大频率为参考时钟的一半（12.5MHz），实际使用建议不超过10MHz。

### Q4: 可以添加更多AD9833吗？
A: 可以。只需要分配不同的GPIO引脚，按照相同的模式创建AD9833_3、AD9833_4等驱动。

## 注意事项

1. **引脚冲突**：确保新分配的PB6/7/8引脚没有被其他功能使用
2. **电源供应**：确保3.3V电源能够提供足够的电流驱动两个模块
3. **信号完整性**：使用较短的连接线，避免信号串扰
4. **时钟同步**：如需精确同步，考虑使用外部同步信号

## 版本信息

- **项目版本**：V1.0
- **创建日期**：2025-07-08
- **适用平台**：野火STM32F429挑战者开发板
- **开发环境**：Keil MDK 5.16 + STM32F4标准库

## 高级功能

### 1. 相位控制
```c
// 设置第一个AD9833的相位
AD9833_SetPhase(AD9833_REG_PHASE0, 0x0000);  // 0度相位

// 设置第二个AD9833的相位
AD9833_2_SetPhase(AD9833_2_REG_PHASE0, 0x0800);  // 90度相位
```

### 2. 频率寄存器切换
```c
// 使用频率寄存器0
AD9833_SetFrequency(AD9833_REG_FREQ0, 1000.0, AD9833_OUT_SINUS);

// 使用频率寄存器1
AD9833_SetFrequency(AD9833_REG_FREQ1, 2000.0, AD9833_OUT_SINUS);

// 在两个频率之间快速切换
AD9833_Setup(AD9833_FSEL0, AD9833_PSEL0, AD9833_OUT_SINUS);  // 选择FREQ0
AD9833_Setup(AD9833_FSEL1, AD9833_PSEL0, AD9833_OUT_SINUS);  // 选择FREQ1
```

### 3. 扫频功能示例
```c
void frequency_sweep_demo(void)
{
    float freq;

    // 第一个AD9833进行扫频：1kHz到10kHz
    for(freq = 1000.0; freq <= 10000.0; freq += 100.0)
    {
        AD9833_SetFrequencyQuick(freq, AD9833_OUT_SINUS);
        Delay_ms(100);  // 每100ms改变一次频率
    }

    // 第二个AD9833保持固定频率
    AD9833_2_SetFrequencyQuick(5000.0, AD9833_2_OUT_TRIANGLE);
}
```

## 调试指南

### 1. 信号检测
使用示波器检测输出信号：
- **通道1**：连接AD9833_1输出
- **通道2**：连接AD9833_2输出
- **触发**：设置为通道1上升沿触发

### 2. 常见故障排除

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 无信号输出 | 电源未连接 | 检查VCC和GND连接 |
| 频率不准确 | 参考时钟错误 | 确认FCLK设置为25MHz |
| 波形失真 | 负载过重 | 使用高阻抗负载或缓冲器 |
| 两路信号干扰 | 布线太近 | 增加信号线间距 |

### 3. 性能测试
```c
void performance_test(void)
{
    uint32_t start_time, end_time;

    // 测试频率设置速度
    start_time = SysTick->VAL;
    AD9833_SetFrequencyQuick(1000.0, AD9833_OUT_SINUS);
    end_time = SysTick->VAL;

    // 计算设置时间（微秒）
    uint32_t setup_time_us = (start_time - end_time) / (SystemCoreClock / 1000000);
}
```

## 扩展应用

### 1. 信号发生器
- 函数发生器
- 音频信号源
- 测试信号发生器

### 2. 通信系统
- FSK调制
- 载波信号生成
- 本振信号源

### 3. 测量仪器
- 扫频仪
- 网络分析仪
- 阻抗分析仪

## 联系支持

如有问题或建议，请参考：
- 野火官网：https://embedfire.com
- 野火论坛：http://www.firebbs.cn
- 康威电子：https://kvdz.taobao.com/
