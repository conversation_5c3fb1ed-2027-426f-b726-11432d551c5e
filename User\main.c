/**
  ******************************************************************************
  * @file    main.c
  * <AUTHOR>
  * @version V1.0
  * @date    2014-xx-xx
  * @brief   使用SysTick进行精确延时
  ******************************************************************************
  * @attention
  *
  * 实验平台:野火  STM32 F429 开发板
  * 论坛    :http://www.firebbs.cn
  * 淘宝    :https://fire-stm32.taobao.com
  *
  ******************************************************************************
  */
  
#include "stm32f4xx.h"
#include "./led/bsp_led.h"
#include "./systick/bsp_SysTick.h"
#include "AD9833.h"	
#include "AD9833_2.h"		
#include "AD9959.h"
#include "usart.h"	
#include "./usart/bsp_debug_usart.h"
#include "ad9226.h"
//#include "./tim/bsp_advance_tim.h"
#include "math.h"
#include "arm_math.h"
#include "fft.h"
#include "adc.h"
#include "timer.h"

/*AD9226配置区*/
#define Draw_Number  			280    			// ???????
#define Sampling_Number  	490    			// ????????

uint16_t AD9226_New_Data[Sampling_Number];
uint16_t AD9226_Data[Sampling_Number];	// 锟斤拷锟捷伙拷锟斤拷锟斤拷
uint16_t Draw_data[Draw_Number]; 				// 锟斤拷锟狡碉拷锟斤拷锟捷伙拷锟斤拷锟斤拷
uint8_t  Time_Flag = 0;									// 锟叫断憋拷志位
uint16_t Gear = 0;											// 采样率档位(全局变量)
//USART_TypeDef * DEBUG_USARTx = USART1;
/*AD9226配置区*/

/*pid配置*/
// ... existing code ... (includes and defines)

// 新增：PID全局变量
double Kp = 0.1;       // 比例系数 (调试: 0.05-0.2)
double Ki = 0.01;      // 积分系数 (调试: 0.005-0.05)
double Kd = 0.05;      // 微分系数 (调试: 0.01-0.1)
double integral = 0.0; // 积分累积
double last_error = 0.0; // 上次误差
double dt = 0.1;       // 时间间隔 (约匹配您的100ms循环)
double fitted_freq1 = 9995.0; // 初始频率 (Hz)，您的代码中已有
double last_voltage = 0.0; // 上次平均电压 (用于变化率)

// ... existing code ...

/*移植锁相环区*/
//bool Separate=false;

extern int t;
extern float fft_outputbuf[4096];
extern u8 Res;

uint32_t frequency_A,frequency_B;
 
int phase_difference_A;
int phase_difference_B;
int phase_difference_A1;
int phase_difference_B1;
extern float phase_A,phase_B,phase;

extern float frequency;
int frequency2_A;
int frequency2_B;
double frequency2_AA,frequency2_BB;

float phase_A_CS=0.0f;
float phase_B_CS=0.0f;
float phase_A_SX=0.0f;
float phase_B_SX=0.0f;

uint32_t peak_idx;
u8 QCZ=100;
u8 QCZ1=0;

int QCZ_Phase[2];
int QCZ_Phase1[2];
int Phase=0;

#define FFT_LENGTH 4096;

float ZE;
int SBP=0;

uint16_t waveform_A;
uint16_t waveform_B;
/*移植锁相环区*/

/**
  * @brief  主函数
  * @param  无
  * @retval 无
  */
int main(void)
{
	/* LED 端口初始化 */
//	LED_GPIO_Config();	 
 
  /* 配置SysTick 为10us中断一次,时间到后触发定时中断，
	*进入stm32fxx_it.c文件的SysTick_Handler处理，通过数中断次数计时
	*/
	
//	/*AD9226配置区*/
//	uint16_t k=0;
//	uint8_t  key,i=0;
//	uint32_t Sampling_Rate[7] = {2300000,250000,100000,50000,10000,5000,1000};// 锟斤拷位Hz
//	uint16_t *pb = AD9226_Data;
//	int j = 10;
////	Gear = 1;
//	/*AD9226配置区*/
//	
//	/*pid配置区*/
//	double sum, avg_value;
//	int N;
//	int m;
//	int crossings;
//	double mid_point;
//	double last_sign;
//	double sign;
//	double sampling_period;
//	double T;
//	 double rate;
//	double error;
//	double derivative;
//	double delta_freq;
//	double max_adjust;
//	/*pid配置区*/	
uint16_t i;
	arm_cfft_radix4_init_f32(&scfft,4096,0,1);//初始化scfft结构体，设定FFT相关参数
	
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);//设置系统中断优先级分组2
	uart_init(115200);
	
	SysTick_Init();
	DMA1_Init();//初始化dma
  DMA2_Init();//初始化dma
  DMA3_Init();//初始化dma
		
	Adc_Init(); //初始化adc
  Adc2_Init();
  Adc3_Init();
	
//		DMA1_Init();//初始化dma
//  DMA2_Init();//初始化dma
//  DMA3_Init();//初始化dma

   
   // 修改2：采样频率 - 适配F429的90MHz定时器时钟
  sampfre=409091;   // 从409756改为409091
		
   // 修改3：定时器3初始化 - 调整预分频器适配90MHz时钟
  TIM3_Int_Init(5-1,44-1);  // PSC从41-1改为44-1
    
  TIM_Cmd(TIM3, ENABLE);

//	AD9226_Init();
	
//	Delay_ms(500);
  
//	dual_ad9833_sync_init();
	
//	AD9833_Init();//IO口及AD9833寄存器初始化
//	
//	AD9833_2_Init();//初始化IO口及寄存器
//	//频率入口参数为float，可使信号的频率更精确
//	AD9833_SetFrequencyQuick(100000.0,AD9833_OUT_SINUS);//写输出频率1000.0Hz,输出正弦波

//	AD9833_2_SetFrequencyQuick(50000.0,AD9833_2_OUT_SINUS);//快速频率及波形设置
	
//	AD9959_Init();								//初始化控制AD9959需要用到的IO口,及寄存器
//	AD9959_Set_Fre(CH0, fitted_freq1);	//设置通道0频率100000Hz
////	AD9959_Set_Fre(CH1, 100000);	//设置通道1频率100000Hz
////	AD9959_Set_Fre(CH2, 100000);	//设置通道2频率100000Hz
////	AD9959_Set_Fre(CH3, 100000);	//设置通道3频率100000Hz

//	AD9959_Set_Amp(CH0, 1023); 		//设置通道0幅度控制值1023，范围0~1023
////	AD9959_Set_Amp(CH1, 1023); 		//设置通道1幅度控制值1023，范围0~1023
////	AD9959_Set_Amp(CH2, 1023); 		//设置通道2幅度控制值1023，范围0~1023
////	AD9959_Set_Amp(CH3, 1023); 		//设置通道3幅度控制值1023，范围0~1023

//	AD9959_Set_Phase(CH0, 0);			//设置通道0相位控制值0(0度)，范围0~16383
////	AD9959_Set_Phase(CH1, 4096);	//设置通道1相位控制值4096(90度)，范围0~16383
////	AD9959_Set_Phase(CH2, 8192);	//设置通道2相位控制值8192(180度)，范围0~16383
////	AD9959_Set_Phase(CH3, 12288);	//设置通道3相位控制值12288(270度)，范围0~16383
//	IO_Update();	//AD9959更新数据,调用此函数后，上述操作生效！！！！
	
	while(1)
	{
//		for(i=0;i<4096;i++){
//		printf("{a}%d\r\n", buff_adc3[i] );
//		}
		
		/*锁相环移植*/
		while(flag_ADC==0||flag_ADC1==0||flag_ADC2==0) {} //等待采样完成
         
         QCZ_FFT(buff_adc); //采集C信号
     
        if(fft_outputbuf[peak1_idx]<500&&fft_outputbuf[peak1_idx]>420)
        { waveform_A=AD9833_OUT_SINUS; }
        if(fft_outputbuf[peak2_idx]<500&&fft_outputbuf[peak2_idx]>420)
        { waveform_B=AD9833_OUT_SINUS; }
        if(fft_outputbuf[peak1_idx]<400)
        { waveform_A=AD9833_OUT_TRIANGLE; }
        if(fft_outputbuf[peak2_idx]<400)
        { waveform_B=AD9833_OUT_TRIANGLE; }

         frequency_A=peak1_idx*100; //得A信号频率
         frequency_B=peak2_idx*100; //得B信号频率
				
				printf("fa:%dhz\r\n", frequency_A );
				printf("fb:%dhz\r\n", frequency_B );
				
//				for(i=0;i<4096;i++){
//		printf("{a}%d\r\n", buff_adc[i] );
		}
			/*锁相环移植*/	
				
				
////		printf("{a}%d\r\n", AD9226_Data[j] & 0x0FFF);
//		if(i>=20)
//	  {
//			if(Sampling_Rate[Gear] == 2300000)	// 锟斤拷锟斤拷锟斤拷为2.3MHz锟斤拷锟矫诧拷锟斤拷锟斤拷取锟斤拷锟斤拷IO锟节碉拷锟劫讹拷
//			{
//				k=Sampling_Number;
//				pb = AD9226_Data;
//				do
//				{
//					GPIOA->BSRRH =  GPIO_Pin_0;
//					*(pb++) = ReadAD9226_Data();
//					GPIOA->BSRRL = GPIO_Pin_0;
//				}while(k--);
//				
////				for(j = 10; j < 60; j++)
////				{
////					printf("{a}%d\r\n", AD9226_Data[j] & 0x0FFF);
////				}
//				for(j = 10; j < 60; j++)
//				{
////					if((AD9226_Data[j] & 0x0FFF)>=2100)
////					{
////						AD9226_New_Data[j]=2420;
//////						AD9226_New_Data[j]=AD9226_Data[j]& 0x0FFF;
////					}
////					else
////						AD9226_New_Data[j]=AD9226_Data[j]& 0x0FFF;
//					
//					AD9226_New_Data[j]=AD9226_Data[j]& 0x0FFF;
//						printf("{a}%d\r\n", AD9226_New_Data[j]);
////					printf("{a}%d\r\n", AD9226_Data[j]& 0x0FFF);
//				}
////			LCD_Wave(&AD9226_Data[10]);	// 锟斤拷锟斤拷AD9226锟侥达拷锟斤拷锟斤拷时10锟斤拷时锟接ｏ拷前10锟斤拷时锟接碉拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
//		}
////			else	// 小锟节碉拷锟斤拷250KHz锟侥诧拷锟斤拷锟斤拷锟矫讹拷时锟斤拷锟斤拷锟斤拷
////			{
////				// F429定时器时钟为90MHz，预分频180，得到500KHz基准
////				TIM_Init(180-1,90000000/180/Sampling_Rate[Gear]-1);
////				while(Time_Flag == 0);
//////				LCD_Wave(&AD9226_Data[10]);	// 锟斤拷锟斤拷AD9226锟侥达拷锟斤拷锟斤拷时10锟斤拷时锟接ｏ拷前10锟斤拷时锟接碉拷锟斤拷锟斤拷锟斤拷锟斤拷锟斤拷
////				for(j = 10; j < 60; j++)
////				{
////					printf("{a}%d\r\n", AD9226_Data[j] & 0x0FFF);
////				}
////				Time_Flag = 0;
////			}
//		
//		                                         /*PID计算*/
//			i=0;
//		}
//		Delay_ms(20);
//		i++;
	}
}

/*********************************************END OF FILE**********************/

