Component: ARM Compiler 5.06 update 5 (build 528) Tool: armlink [4d35e2]

==============================================================================

Section Cross References

    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    startup_stm32f429_439xx.o(RESET) refers to startup_stm32f429_439xx.o(.text) for Reset_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.NMI_Handler) for NMI_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.HardFault_Handler) for HardFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.MemManage_Handler) for MemManage_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.BusFault_Handler) for BusFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.UsageFault_Handler) for UsageFault_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.SVC_Handler) for SVC_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.DebugMon_Handler) for DebugMon_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.PendSV_Handler) for PendSV_Handler
    startup_stm32f429_439xx.o(RESET) refers to stm32f4xx_it.o(i.SysTick_Handler) for SysTick_Handler
    startup_stm32f429_439xx.o(RESET) refers to usart.o(i.USART1_IRQHandler) for USART1_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to adc.o(i.DMA2_Stream0_IRQHandler) for DMA2_Stream0_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to adc.o(i.DMA2_Stream1_IRQHandler) for DMA2_Stream1_IRQHandler
    startup_stm32f429_439xx.o(RESET) refers to adc.o(i.DMA2_Stream2_IRQHandler) for DMA2_Stream2_IRQHandler
    startup_stm32f429_439xx.o(.text) refers to system_stm32f4xx.o(i.SystemInit) for SystemInit
    startup_stm32f429_439xx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    system_stm32f4xx.o(i.SystemCoreClockUpdate) refers to system_stm32f4xx.o(.data) for .data
    system_stm32f4xx.o(i.SystemInit) refers to system_stm32f4xx.o(i.SetSysClock) for SetSysClock
    stm32f4xx_adc.o(i.ADC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_can.o(i.CAN_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_can.o(i.CAN_GetITStatus) refers to stm32f4xx_can.o(i.CheckITStatus) for CheckITStatus
    stm32f4xx_cryp.o(i.CRYP_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_PhaseConfig) for CRYP_PhaseConfig
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_des.o(i.CRYP_DES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_IVInit) for CRYP_IVInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyStructInit) for CRYP_KeyStructInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Init) for CRYP_Init
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_KeyInit) for CRYP_KeyInit
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_FIFOFlush) for CRYP_FIFOFlush
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_Cmd) for CRYP_Cmd
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetCmdStatus) for CRYP_GetCmdStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataIn) for CRYP_DataIn
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_GetFlagStatus) for CRYP_GetFlagStatus
    stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB) refers to stm32f4xx_cryp.o(i.CRYP_DataOut) for CRYP_DataOut
    stm32f4xx_dac.o(i.DAC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_dma2d.o(i.DMA2D_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseAllSectors) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_EraseSector) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_Launch) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_RDPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_UserConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRP1Config) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_OB_WRPConfig) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramByte) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramHalfWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_ProgramWord) refers to stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) for FLASH_WaitForLastOperation
    stm32f4xx_flash.o(i.FLASH_WaitForLastOperation) refers to stm32f4xx_flash.o(i.FLASH_GetStatus) for FLASH_GetStatus
    stm32f4xx_fmc.o(i.FMC_NORSRAMStructInit) refers to stm32f4xx_fmc.o(.constdata) for .constdata
    stm32f4xx_gpio.o(i.GPIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd) for RCC_AHB1PeriphResetCmd
    stm32f4xx_hash.o(i.HASH_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HASH_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_md5.o(i.HMAC_MD5) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HASH_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DeInit) for HASH_DeInit
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_Init) for HASH_Init
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr) for HASH_SetLastWordValidBitsNbr
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_DataIn) for HASH_DataIn
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_StartDigest) for HASH_StartDigest
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetFlagStatus) for HASH_GetFlagStatus
    stm32f4xx_hash_sha1.o(i.HMAC_SHA1) refers to stm32f4xx_hash.o(i.HASH_GetDigest) for HASH_GetDigest
    stm32f4xx_i2c.o(i.I2C_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_i2c.o(i.I2C_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_ltdc.o(i.LTDC_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_pwr.o(i.PWR_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_rcc.o(i.RCC_GetClocksFreq) refers to stm32f4xx_rcc.o(.data) for .data
    stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp) refers to stm32f4xx_rcc.o(i.RCC_GetFlagStatus) for RCC_GetFlagStatus
    stm32f4xx_rng.o(i.RNG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd) for RCC_AHB2PeriphResetCmd
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_DeInit) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_GetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_GetTimeStamp) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_Init) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_RefClockCmd) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetAlarm) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetDate) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_Bcd2ToByte) for RTC_Bcd2ToByte
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ByteToBcd2) for RTC_ByteToBcd2
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_EnterInitMode) for RTC_EnterInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_ExitInitMode) for RTC_ExitInitMode
    stm32f4xx_rtc.o(i.RTC_SetTime) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig) refers to stm32f4xx_rtc.o(i.RTC_WaitForSynchro) for RTC_WaitForSynchro
    stm32f4xx_sai.o(i.SAI_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_sdio.o(i.SDIO_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_spi.o(i.SPI_I2S_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_syscfg.o(i.SYSCFG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_tim.o(i.TIM_ETRClockMode1Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ETRClockMode2Config) refers to stm32f4xx_tim.o(i.TIM_ETRConfig) for TIM_ETRConfig
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI4_Config) for TI4_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC4Prescaler) for TIM_SetIC4Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TI3_Config) for TI3_Config
    stm32f4xx_tim.o(i.TIM_ICInit) refers to stm32f4xx_tim.o(i.TIM_SetIC3Prescaler) for TIM_SetIC3Prescaler
    stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC2Prescaler) for TIM_SetIC2Prescaler
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_PWMIConfig) refers to stm32f4xx_tim.o(i.TIM_SetIC1Prescaler) for TIM_SetIC1Prescaler
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI1_Config) for TI1_Config
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TIM_SelectInputTrigger) for TIM_SelectInputTrigger
    stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig) refers to stm32f4xx_tim.o(i.TI2_Config) for TI2_Config
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(i.USART_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    stm32f4xx_usart.o(i.USART_Init) refers to stm32f4xx_rcc.o(i.RCC_GetClocksFreq) for RCC_GetClocksFreq
    stm32f4xx_wwdg.o(i.WWDG_DeInit) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd) for RCC_APB1PeriphResetCmd
    main.o(i.main) refers to arm_cfft_radix4_init_f32.o(.text) for arm_cfft_radix4_init_f32
    main.o(i.main) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    main.o(i.main) refers to usart.o(i.uart_init) for uart_init
    main.o(i.main) refers to bsp_systick.o(i.SysTick_Init) for SysTick_Init
    main.o(i.main) refers to adc.o(i.DMA1_Init) for DMA1_Init
    main.o(i.main) refers to adc.o(i.DMA2_Init) for DMA2_Init
    main.o(i.main) refers to adc.o(i.DMA3_Init) for DMA3_Init
    main.o(i.main) refers to adc.o(i.Adc_Init) for Adc_Init
    main.o(i.main) refers to adc.o(i.Adc2_Init) for Adc2_Init
    main.o(i.main) refers to adc.o(i.Adc3_Init) for Adc3_Init
    main.o(i.main) refers to timer.o(i.TIM3_Int_Init) for TIM3_Int_Init
    main.o(i.main) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    main.o(i.main) refers to printf1.o(i.__0printf$1) for __2printf
    main.o(i.main) refers to adc.o(i.QCZ_FFT) for QCZ_FFT
    main.o(i.main) refers to fft.o(.bss) for scfft
    main.o(i.main) refers to fft.o(.data) for sampfre
    main.o(i.main) refers to adc.o(.bss) for buff_adc3
    main.o(i.main) refers to main.o(.data) for .data
    main.o(i.main) refers to adc.o(.data) for flag_ADC
    stm32f4xx_it.o(i.SysTick_Handler) refers to bsp_systick.o(i.TimingDelay_Decrement) for TimingDelay_Decrement
    bsp_led.o(i.LED_GPIO_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_led.o(i.LED_GPIO_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_systick.o(i.Delay_us) refers to bsp_systick.o(.data) for .data
    bsp_systick.o(i.SysTick_Init) refers to system_stm32f4xx.o(.data) for SystemCoreClock
    bsp_systick.o(i.TimingDelay_Decrement) refers to bsp_systick.o(.data) for .data
    ad9833.o(i.AD9833_ClearReset) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_Init) refers to ad9833.o(i.AD983_GPIO_Init) for AD983_GPIO_Init
    ad9833.o(i.AD9833_Init) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_Reset) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_Reset) refers to bsp_systick.o(i.Delay_us) for Delay_us
    ad9833.o(i.AD9833_SetFrequency) refers to f2d.o(.text) for __aeabi_f2d
    ad9833.o(i.AD9833_SetFrequency) refers to dmul.o(.text) for __aeabi_dmul
    ad9833.o(i.AD9833_SetFrequency) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9833.o(i.AD9833_SetFrequency) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_SetFrequencyQuick) refers to ad9833.o(i.AD9833_SetFrequency) for AD9833_SetFrequency
    ad9833.o(i.AD9833_SetPhase) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_SetRegisterValue) refers to ad9833.o(i.AD9833_SPI_Write) for AD9833_SPI_Write
    ad9833.o(i.AD9833_SetRegisterValue) refers to ad9833.o(.constdata) for .constdata
    ad9833.o(i.AD9833_SetWave) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD9833_Setup) refers to ad9833.o(i.AD9833_SetRegisterValue) for AD9833_SetRegisterValue
    ad9833.o(i.AD983_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9833.o(i.AD983_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9833_2.o(i.AD9833_2_ClearReset) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_Init) refers to ad9833_2.o(i.AD983_2_GPIO_Init) for AD983_2_GPIO_Init
    ad9833_2.o(i.AD9833_2_Init) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_Reset) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_Reset) refers to bsp_systick.o(i.Delay_us) for Delay_us
    ad9833_2.o(i.AD9833_2_SetFrequency) refers to f2d.o(.text) for __aeabi_f2d
    ad9833_2.o(i.AD9833_2_SetFrequency) refers to dmul.o(.text) for __aeabi_dmul
    ad9833_2.o(i.AD9833_2_SetFrequency) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9833_2.o(i.AD9833_2_SetFrequency) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_SetFrequencyQuick) refers to ad9833_2.o(i.AD9833_2_SetFrequency) for AD9833_2_SetFrequency
    ad9833_2.o(i.AD9833_2_SetPhase) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_SetRegisterValue) refers to ad9833_2.o(i.AD9833_2_SPI_Write) for AD9833_2_SPI_Write
    ad9833_2.o(i.AD9833_2_SetRegisterValue) refers to ad9833_2.o(.constdata) for .constdata
    ad9833_2.o(i.AD9833_2_SetWave) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD9833_2_Setup) refers to ad9833_2.o(i.AD9833_2_SetRegisterValue) for AD9833_2_SetRegisterValue
    ad9833_2.o(i.AD983_2_GPIO_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9833_2.o(i.AD983_2_GPIO_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9959.o(i.AD9959_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9959.o(i.AD9959_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.Intserve) for Intserve
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.IntReset) for IntReset
    ad9959.o(i.AD9959_Init) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_Init) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959_Modulation_Init) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_Modulation_Init) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959_SetASK) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetASK) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetASK) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetASK) refers to ad9959.o(i.Write_ACR) for Write_ACR
    ad9959.o(i.AD9959_SetASK) refers to ad9959.o(i.Write_Profile_Ampli) for Write_Profile_Ampli
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_LSRR) for Write_LSRR
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_RDW) for Write_RDW
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_FDW) for Write_FDW
    ad9959.o(i.AD9959_SetAmp_Sweep) refers to ad9959.o(i.Write_Profile_Ampli) for Write_Profile_Ampli
    ad9959.o(i.AD9959_SetFSK) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetFSK) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetFSK) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetFSK) refers to ad9959.o(i.Write_Profile_Fre) for Write_Profile_Fre
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_ACR) for Write_ACR
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_LSRR) for Write_LSRR
    ad9959.o(i.AD9959_SetFre_Sweep) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.AD9959_SetFre_Sweep) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.AD9959_SetFre_Sweep) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_RDW) for Write_RDW
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_FDW) for Write_FDW
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(i.Write_Profile_Fre) for Write_Profile_Fre
    ad9959.o(i.AD9959_SetFre_Sweep) refers to ad9959.o(.data) for .data
    ad9959.o(i.AD9959_SetPSK) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetPSK) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetPSK) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetPSK) refers to ad9959.o(i.Write_Profile_Phase) for Write_Profile_Phase
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_ACR) for Write_ACR
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_LSRR) for Write_LSRR
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_RDW) for Write_RDW
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_FDW) for Write_FDW
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.AD9959_SetPhase_Sweep) refers to ad9959.o(i.Write_Profile_Phase) for Write_Profile_Phase
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_Set_Amp) refers to ad9959.o(i.Write_ACR) for Write_ACR
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_Set_Fre) refers to ad9959.o(i.Write_CFTW0) for Write_CFTW0
    ad9959.o(i.AD9959_Set_Phase) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.AD9959_Set_Phase) refers to ad9959.o(i.Write_CPOW0) for Write_CPOW0
    ad9959.o(i.IO_Update) refers to ad9959.o(i.delay1) for delay1
    ad9959.o(i.IntReset) refers to ad9959.o(i.delay1) for delay1
    ad9959.o(i.Write_ACR) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_CFTW0) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.Write_CFTW0) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.Write_CFTW0) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.Write_CFTW0) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_CFTW0) refers to ad9959.o(.data) for .data
    ad9959.o(i.Write_CPOW0) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_FDW) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_LSRR) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_Profile_Ampli) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_Profile_Fre) refers to dfltui.o(.text) for __aeabi_ui2d
    ad9959.o(i.Write_Profile_Fre) refers to dmul.o(.text) for __aeabi_dmul
    ad9959.o(i.Write_Profile_Fre) refers to dfixui.o(.text) for __aeabi_d2uiz
    ad9959.o(i.Write_Profile_Fre) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_Profile_Fre) refers to ad9959.o(.data) for .data
    ad9959.o(i.Write_Profile_Phase) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    ad9959.o(i.Write_RDW) refers to ad9959.o(i.AD9959_WriteData) for AD9959_WriteData
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_GetITStatus) for USART_GetITStatus
    usart.o(i.USART1_IRQHandler) refers to stm32f4xx_usart.o(i.USART_ReceiveData) for USART_ReceiveData
    usart.o(i.USART1_IRQHandler) refers to usart.o(.data) for .data
    usart.o(i.USART1_IRQHandler) refers to usart.o(.bss) for .bss
    usart.o(i.fputc) refers to usart.o(.data) for .data
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    usart.o(i.uart_init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    usart.o(i.uart_init) refers to stm32f4xx_usart.o(i.USART_ITConfig) for USART_ITConfig
    usart.o(i.uart_init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    ad9226.o(i.AD9226_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    ad9226.o(i.AD9226_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_gpio.o(i.GPIO_PinAFConfig) for GPIO_PinAFConfig
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_usart.o(i.USART_Init) for USART_Init
    bsp_debug_usart.o(i.Debug_USART_Config) refers to stm32f4xx_usart.o(i.USART_Cmd) for USART_Cmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc2_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) for ADC_DiscModeChannelCountConfig
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeCmd) for ADC_DiscModeCmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.Adc2_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc3_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) for ADC_DiscModeChannelCountConfig
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeCmd) for ADC_DiscModeCmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.Adc3_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd) for RCC_APB2PeriphClockCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_gpio.o(i.GPIO_Init) for GPIO_Init
    adc.o(i.Adc_Init) refers to stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd) for RCC_APB2PeriphResetCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_CommonInit) for ADC_CommonInit
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_Init) for ADC_Init
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_RegularChannelConfig) for ADC_RegularChannelConfig
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig) for ADC_DiscModeChannelCountConfig
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_DiscModeCmd) for ADC_DiscModeCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd) for ADC_DMARequestAfterLastTransferCmd
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_DMACmd) for ADC_DMACmd
    adc.o(i.Adc_Init) refers to stm32f4xx_adc.o(i.ADC_Cmd) for ADC_Cmd
    adc.o(i.DMA1_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    adc.o(i.DMA1_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    adc.o(i.DMA1_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    adc.o(i.DMA1_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.DMA1_Init) refers to adc.o(.bss) for .bss
    adc.o(i.DMA2_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    adc.o(i.DMA2_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    adc.o(i.DMA2_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    adc.o(i.DMA2_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.DMA2_Init) refers to adc.o(.bss) for .bss
    adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc.o(i.DMA2_Stream0_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA2_Stream0_IRQHandler) refers to adc.o(.data) for .data
    adc.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    adc.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc.o(i.DMA2_Stream1_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA2_Stream1_IRQHandler) refers to adc.o(.data) for .data
    adc.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_GetITStatus) for DMA_GetITStatus
    adc.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    adc.o(i.DMA2_Stream2_IRQHandler) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA2_Stream2_IRQHandler) refers to adc.o(.data) for .data
    adc.o(i.DMA3_Init) refers to misc.o(i.NVIC_PriorityGroupConfig) for NVIC_PriorityGroupConfig
    adc.o(i.DMA3_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    adc.o(i.DMA3_Init) refers to stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd) for RCC_AHB1PeriphClockCmd
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_DeInit) for DMA_DeInit
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_GetCmdStatus) for DMA_GetCmdStatus
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_Init) for DMA_Init
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_ClearITPendingBit) for DMA_ClearITPendingBit
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_ITConfig) for DMA_ITConfig
    adc.o(i.DMA3_Init) refers to stm32f4xx_dma.o(i.DMA_Cmd) for DMA_Cmd
    adc.o(i.DMA3_Init) refers to adc.o(.bss) for .bss
    adc.o(i.QCZ_FFT) refers to fft.o(i.Hanningwindow) for Hanningwindow
    adc.o(i.QCZ_FFT) refers to fft.o(i.FFT) for FFT
    adc.o(i.QCZ_FFT) refers to f2d.o(.text) for __aeabi_f2d
    adc.o(i.QCZ_FFT) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    adc.o(i.QCZ_FFT) refers to dmul.o(.text) for __aeabi_dmul
    adc.o(i.QCZ_FFT) refers to ddiv.o(.text) for __aeabi_ddiv
    adc.o(i.QCZ_FFT) refers to d2f.o(.text) for __aeabi_d2f
    adc.o(i.QCZ_FFT) refers to fft.o(.bss) for fft_inputbuf
    adc.o(i.QCZ_FFT) refers to fft.o(.data) for peak1_idx
    adc.o(i.QCZ_FFT) refers to adc.o(.data) for .data
    adc.o(i.QCZ_FFT1) refers to fft.o(i.Hanningwindow) for Hanningwindow
    adc.o(i.QCZ_FFT1) refers to fft.o(i.FFT) for FFT
    adc.o(i.QCZ_FFT1) refers to fft.o(i.get_basefrevpp) for get_basefrevpp
    adc.o(i.QCZ_FFT1) refers to f2d.o(.text) for __aeabi_f2d
    adc.o(i.QCZ_FFT1) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    adc.o(i.QCZ_FFT1) refers to dmul.o(.text) for __aeabi_dmul
    adc.o(i.QCZ_FFT1) refers to ddiv.o(.text) for __aeabi_ddiv
    adc.o(i.QCZ_FFT1) refers to d2f.o(.text) for __aeabi_d2f
    adc.o(i.QCZ_FFT1) refers to fft.o(.bss) for fft_inputbuf
    adc.o(i.QCZ_FFT1) refers to fft.o(.data) for timef
    adc.o(i.QCZ_FFT1) refers to adc.o(.data) for .data
    timer.o(i.TIM3_Int_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM3_Int_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM3_Int_Init) refers to stm32f4xx_tim.o(i.TIM_SelectOutputTrigger) for TIM_SelectOutputTrigger
    timer.o(i.TIM4_Int_Init) refers to stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd) for RCC_APB1PeriphClockCmd
    timer.o(i.TIM4_Int_Init) refers to stm32f4xx_tim.o(i.TIM_TimeBaseInit) for TIM_TimeBaseInit
    timer.o(i.TIM4_Int_Init) refers to stm32f4xx_tim.o(i.TIM_ITConfig) for TIM_ITConfig
    timer.o(i.TIM4_Int_Init) refers to stm32f4xx_tim.o(i.TIM_Cmd) for TIM_Cmd
    timer.o(i.TIM4_Int_Init) refers to misc.o(i.NVIC_Init) for NVIC_Init
    fft.o(i.FFT) refers to dfltui.o(.text) for __aeabi_ui2d
    fft.o(i.FFT) refers to dmul.o(.text) for __aeabi_dmul
    fft.o(i.FFT) refers to d2f.o(.text) for __aeabi_d2f
    fft.o(i.FFT) refers to arm_cfft_radix4_f32.o(.text) for arm_cfft_radix4_f32
    fft.o(i.FFT) refers to arm_cmplx_mag_f32.o(.text) for arm_cmplx_mag_f32
    fft.o(i.FFT) refers to fft.o(i.find_peak_indices) for find_peak_indices
    fft.o(i.FFT) refers to fft.o(.data) for .data
    fft.o(i.FFT) refers to fft.o(.bss) for .bss
    fft.o(i.Get_vpp_fre) refers to fft.o(i.get_pianyik) for get_pianyik
    fft.o(i.Get_vpp_fre) refers to f2d.o(.text) for __aeabi_f2d
    fft.o(i.Get_vpp_fre) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    fft.o(i.Get_vpp_fre) refers to ddiv.o(.text) for __aeabi_ddiv
    fft.o(i.Get_vpp_fre) refers to dmul.o(.text) for __aeabi_dmul
    fft.o(i.Get_vpp_fre) refers to d2f.o(.text) for __aeabi_d2f
    fft.o(i.Get_vpp_fre) refers to fft.o(.data) for .data
    fft.o(i.Get_vpp_fre) refers to fft.o(.bss) for .bss
    fft.o(i.Hanningwindow) refers to f2d.o(.text) for __aeabi_f2d
    fft.o(i.Hanningwindow) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    fft.o(i.Hanningwindow) refers to dadd.o(.text) for __aeabi_drsub
    fft.o(i.Hanningwindow) refers to dmul.o(.text) for __aeabi_dmul
    fft.o(i.Hanningwindow) refers to d2f.o(.text) for __aeabi_d2f
    fft.o(i.get_basefrevpp) refers to fft.o(i.Get_basevpp_point) for Get_basevpp_point
    fft.o(i.get_basefrevpp) refers to fft.o(i.get_pianyik) for get_pianyik
    fft.o(i.get_basefrevpp) refers to f2d.o(.text) for __aeabi_f2d
    fft.o(i.get_basefrevpp) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    fft.o(i.get_basefrevpp) refers to ddiv.o(.text) for __aeabi_ddiv
    fft.o(i.get_basefrevpp) refers to dmul.o(.text) for __aeabi_dmul
    fft.o(i.get_basefrevpp) refers to d2f.o(.text) for __aeabi_d2f
    fft.o(i.get_basefrevpp) refers to fft.o(.bss) for .bss
    fft.o(i.get_basefrevpp) refers to fft.o(.data) for .data
    fft.o(i.get_pianyik) refers to fft.o(.bss) for .bss
    fft.o(i.n_get_vppfre) refers to fft.o(i.Get_basevpp_point) for Get_basevpp_point
    fft.o(i.n_get_vppfre) refers to fft.o(i.Get_othervpp_point) for Get_othervpp_point
    fft.o(i.n_get_vppfre) refers to fft.o(i.Get_vpp_fre) for Get_vpp_fre
    fft.o(i.n_get_vppfre) refers to memcpya.o(.text) for __aeabi_memcpy4
    fft.o(i.n_get_vppfre) refers to fft.o(.data) for .data
    arm_cfft_radix4_f32.o(.text) refers to arm_bitreversal.o(.text) for arm_bitreversal_f32
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for twiddleCoef_4096
    arm_cfft_radix4_init_f32.o(.text) refers to arm_common_tables.o(.constdata) for armBitRevTable
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    cos.o(i.__hardfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__hardfp_cos) refers to errno.o(i.__set_errno) for __set_errno
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    cos.o(i.__hardfp_cos) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos.o(i.__hardfp_cos) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos.o(i.__hardfp_cos) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos.o(i.__hardfp_cos) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos.o(i.__softfp_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.__softfp_cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos.o(i.cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos.o(i.cos) refers to cos.o(i.__hardfp_cos) for __hardfp_cos
    cos_x.o(i.____hardfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____hardfp_cos$lsc) refers to errno.o(i.__set_errno) for __set_errno
    cos_x.o(i.____hardfp_cos$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    cos_x.o(i.____hardfp_cos$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    cos_x.o(i.____hardfp_cos$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    cos_x.o(i.____hardfp_cos$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    cos_x.o(i.____softfp_cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.____softfp_cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    cos_x.o(i.__cos$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_x.o(i.__cos$lsc) refers to cos_x.o(i.____hardfp_cos$lsc) for ____hardfp_cos$lsc
    sin.o(i.__hardfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__hardfp_sin) refers to errno.o(i.__set_errno) for __set_errno
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    sin.o(i.__hardfp_sin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin.o(i.__hardfp_sin) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin.o(i.__hardfp_sin) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin.o(i.__hardfp_sin) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin.o(i.__softfp_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.__softfp_sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin.o(i.sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin.o(i.sin) refers to sin.o(i.__hardfp_sin) for __hardfp_sin
    sin_x.o(i.____hardfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____hardfp_sin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sin_x.o(i.____hardfp_sin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    sin_x.o(i.____hardfp_sin$lsc) refers to rred.o(i.__ieee754_rem_pio2) for __ieee754_rem_pio2
    sin_x.o(i.____hardfp_sin$lsc) refers to cos_i.o(i.__kernel_cos) for __kernel_cos
    sin_x.o(i.____hardfp_sin$lsc) refers to sin_i.o(i.__kernel_sin) for __kernel_sin
    sin_x.o(i.____softfp_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.____softfp_sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    sin_x.o(i.__sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_x.o(i.__sin$lsc) refers to sin_x.o(i.____hardfp_sin$lsc) for ____hardfp_sin$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000D) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$0000000F) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(i.fputc) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(i.fputc) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(i.fputc) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(i.fputc) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(i.fputc) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(i.fputc) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(i.fputc) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(i.fputc) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(i.fputc) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(i.fputc) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(i.fputc) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dfltui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfltui.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixui.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dfixui.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers (Special) to iusefp.o(.text) for __I$use$fp
    cos_i.o(i.__kernel_cos) refers to dfixi.o(.text) for __aeabi_d2iz
    cos_i.o(i.__kernel_cos) refers to dmul.o(.text) for __aeabi_dmul
    cos_i.o(i.__kernel_cos) refers to poly.o(i.__kernel_poly) for __kernel_poly
    cos_i.o(i.__kernel_cos) refers to dadd.o(.text) for __aeabi_dsub
    cos_i.o(i.__kernel_cos) refers to cos_i.o(.constdata) for .constdata
    cos_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(i.__ieee754_rem_pio2) refers to dadd.o(.text) for __aeabi_dsub
    rred.o(i.__ieee754_rem_pio2) refers to fabs.o(i.fabs) for fabs
    rred.o(i.__ieee754_rem_pio2) refers to dmul.o(.text) for __aeabi_dmul
    rred.o(i.__ieee754_rem_pio2) refers to dfixi.o(.text) for __aeabi_d2iz
    rred.o(i.__ieee754_rem_pio2) refers to dflti.o(.text) for __aeabi_i2d
    rred.o(i.__ieee754_rem_pio2) refers to dfltui.o(.text) for __aeabi_ui2d
    rred.o(i.__ieee754_rem_pio2) refers to rred.o(.constdata) for .constdata
    rred.o(i.__use_accurate_range_reduction) refers (Special) to iusefp.o(.text) for __I$use$fp
    rred.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i.o(i.__kernel_sin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    sin_i.o(i.__kernel_sin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    sin_i.o(i.__kernel_sin) refers to dmul.o(.text) for __aeabi_dmul
    sin_i.o(i.__kernel_sin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i.o(i.__kernel_sin) refers to dadd.o(.text) for __aeabi_dsub
    sin_i.o(i.__kernel_sin) refers to sin_i.o(.constdata) for .constdata
    sin_i.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sin_i_x.o(i.____kernel_sin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    sin_i_x.o(i.____kernel_sin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    sin_i_x.o(i.____kernel_sin$lsc) refers to dadd.o(.text) for __aeabi_dsub
    sin_i_x.o(i.____kernel_sin$lsc) refers to sin_i_x.o(.constdata) for .constdata
    sin_i_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f429_439xx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(i.main) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(i.main) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dfixi.o(.text) refers to llushr.o(.text) for __aeabi_llsr


==============================================================================

Removing Unused input sections from the image.

    Removing startup_stm32f429_439xx.o(HEAP), (512 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rrx_text), (6 bytes).
    Removing system_stm32f4xx.o(i.SystemCoreClockUpdate), (136 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing misc.o(.rrx_text), (6 bytes).
    Removing misc.o(i.NVIC_SetVectorTable), (20 bytes).
    Removing misc.o(i.NVIC_SystemLPConfig), (28 bytes).
    Removing misc.o(i.SysTick_CLKSourceConfig), (28 bytes).
    Removing stm32f4xx_adc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_adc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_adc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogCmd), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogSingleChannelConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AnalogWatchdogThresholdsConfig), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_AutoInjectedConvCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearFlag), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_adc.o(i.ADC_CommonStructInit), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ContinuousModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_DeInit), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_EOCOnEachRegularChannelCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ExternalTrigInjectedConvEdgeConfig), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetConversionValue), (6 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetITStatus), (30 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetInjectedConversionValue), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetMultiModeConversionValue), (12 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartConvStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_GetSoftwareStartInjectedConvCmdStatus), (14 bytes).
    Removing stm32f4xx_adc.o(i.ADC_ITConfig), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedChannelConfig), (74 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedDiscModeCmd), (24 bytes).
    Removing stm32f4xx_adc.o(i.ADC_InjectedSequencerLengthConfig), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_MultiModeDMARequestAfterLastTransferCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SetInjectedOffset), (16 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_SoftwareStartInjectedConv), (10 bytes).
    Removing stm32f4xx_adc.o(i.ADC_StructInit), (20 bytes).
    Removing stm32f4xx_adc.o(i.ADC_TempSensorVrefintCmd), (40 bytes).
    Removing stm32f4xx_adc.o(i.ADC_VBATCmd), (40 bytes).
    Removing stm32f4xx_can.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_can.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_can.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_CancelTransmit), (42 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearFlag), (48 bytes).
    Removing stm32f4xx_can.o(i.CAN_ClearITPendingBit), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_DBGFreeze), (24 bytes).
    Removing stm32f4xx_can.o(i.CAN_DeInit), (56 bytes).
    Removing stm32f4xx_can.o(i.CAN_FIFORelease), (24 bytes).
    Removing stm32f4xx_can.o(i.CAN_FilterInit), (212 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetFlagStatus), (82 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetITStatus), (204 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLSBTransmitErrorCounter), (8 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetLastErrorCode), (10 bytes).
    Removing stm32f4xx_can.o(i.CAN_GetReceiveErrorCounter), (6 bytes).
    Removing stm32f4xx_can.o(i.CAN_ITConfig), (20 bytes).
    Removing stm32f4xx_can.o(i.CAN_Init), (260 bytes).
    Removing stm32f4xx_can.o(i.CAN_MessagePending), (28 bytes).
    Removing stm32f4xx_can.o(i.CAN_OperatingModeRequest), (154 bytes).
    Removing stm32f4xx_can.o(i.CAN_Receive), (130 bytes).
    Removing stm32f4xx_can.o(i.CAN_SlaveStartBank), (44 bytes).
    Removing stm32f4xx_can.o(i.CAN_Sleep), (30 bytes).
    Removing stm32f4xx_can.o(i.CAN_StructInit), (32 bytes).
    Removing stm32f4xx_can.o(i.CAN_TTComModeCmd), (96 bytes).
    Removing stm32f4xx_can.o(i.CAN_Transmit), (182 bytes).
    Removing stm32f4xx_can.o(i.CAN_TransmitStatus), (136 bytes).
    Removing stm32f4xx_can.o(i.CAN_WakeUp), (40 bytes).
    Removing stm32f4xx_can.o(i.CheckITStatus), (12 bytes).
    Removing stm32f4xx_cec.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cec.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cec.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_crc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_crc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcBlockCRC), (28 bytes).
    Removing stm32f4xx_crc.o(i.CRC_CalcCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetCRC), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_GetIDRegister), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_ResetDR), (12 bytes).
    Removing stm32f4xx_crc.o(i.CRC_SetIDRegister), (12 bytes).
    Removing stm32f4xx_cryp.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Cmd), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DMACmd), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataIn), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DataOut), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_DeInit), (22 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_FIFOFlush), (16 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetCmdStatus), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetFlagStatus), (28 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_GetITStatus), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_ITConfig), (32 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVInit), (24 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_IVStructInit), (12 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_Init), (96 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyInit), (40 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_KeyStructInit), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_PhaseConfig), (20 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_RestoreContext), (140 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_SaveContext), (220 bytes).
    Removing stm32f4xx_cryp.o(i.CRYP_StructInit), (12 bytes).
    Removing stm32f4xx_cryp_aes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_aes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CBC), (454 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CCM), (1494 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_CTR), (394 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_ECB), (416 bytes).
    Removing stm32f4xx_cryp_aes.o(i.CRYP_AES_GCM), (1180 bytes).
    Removing stm32f4xx_cryp_des.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_des.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_CBC), (220 bytes).
    Removing stm32f4xx_cryp_des.o(i.CRYP_DES_ECB), (198 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_cryp_tdes.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_CBC), (252 bytes).
    Removing stm32f4xx_cryp_tdes.o(i.CRYP_TDES_ECB), (230 bytes).
    Removing stm32f4xx_dac.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dac.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dac.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearFlag), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Cmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DMACmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DeInit), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_DualSoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetDataOutputValue), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetFlagStatus), (24 bytes).
    Removing stm32f4xx_dac.o(i.DAC_GetITStatus), (36 bytes).
    Removing stm32f4xx_dac.o(i.DAC_ITConfig), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_Init), (40 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel1Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetChannel2Data), (20 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SetDualChannelData), (28 bytes).
    Removing stm32f4xx_dac.o(i.DAC_SoftwareTriggerCmd), (32 bytes).
    Removing stm32f4xx_dac.o(i.DAC_StructInit), (12 bytes).
    Removing stm32f4xx_dac.o(i.DAC_WaveGenerationCmd), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dbgmcu.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB1PeriphConfig), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_APB2PeriphConfig), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_Config), (28 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetDEVID), (16 bytes).
    Removing stm32f4xx_dbgmcu.o(i.DBGMCU_GetREVID), (12 bytes).
    Removing stm32f4xx_dcmi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dcmi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CROPConfig), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_CaptureCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearFlag), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Cmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_DeInit), (28 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetFlagStatus), (40 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_GetITStatus), (24 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ITConfig), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_Init), (60 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_JPEGCmd), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_ReadData), (12 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_SetEmbeddedSynchroCodes), (32 bytes).
    Removing stm32f4xx_dcmi.o(i.DCMI_StructInit), (18 bytes).
    Removing stm32f4xx_dma.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_ClearFlag), (44 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeCmd), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_DoubleBufferModeConfig), (26 bytes).
    Removing stm32f4xx_dma.o(i.DMA_FlowControllerConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrDataCounter), (6 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetCurrentMemoryTarget), (14 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFIFOStatus), (8 bytes).
    Removing stm32f4xx_dma.o(i.DMA_GetFlagStatus), (52 bytes).
    Removing stm32f4xx_dma.o(i.DMA_MemoryTargetConfig), (12 bytes).
    Removing stm32f4xx_dma.o(i.DMA_PeriphIncOffsetSizeConfig), (24 bytes).
    Removing stm32f4xx_dma.o(i.DMA_SetCurrDataCounter), (4 bytes).
    Removing stm32f4xx_dma.o(i.DMA_StructInit), (34 bytes).
    Removing stm32f4xx_dma2d.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_dma2d.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_AbortTransfer), (16 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGConfig), (108 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BGStart), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_BG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearFlag), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_DeadTimeConfig), (44 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGConfig), (108 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FGStart), (32 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_FG_StructInit), (26 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_GetITStatus), (36 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_ITConfig), (28 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Init), (184 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_LineWatermarkConfig), (12 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StartTransfer), (16 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_StructInit), (24 bytes).
    Removing stm32f4xx_dma2d.o(i.DMA2D_Suspend), (32 bytes).
    Removing stm32f4xx_exti.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_exti.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_exti.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearFlag), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_DeInit), (36 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GenerateSWInterrupt), (16 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_GetITStatus), (20 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_Init), (116 bytes).
    Removing stm32f4xx_exti.o(i.EXTI_StructInit), (14 bytes).
    Removing stm32f4xx_flash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_DataCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank1Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllBank2Sectors), (92 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseAllSectors), (88 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_EraseSector), (112 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_GetStatus), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ITConfig), (28 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_InstructionCacheReset), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BORConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_BootConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetBOR), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetPCROP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetRDP), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetUser), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_GetWRP1), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Launch), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Lock), (16 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_PCROPSelectionConfig), (20 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_RDPConfig), (24 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_UserConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRP1Config), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_OB_WRPConfig), (40 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_PrefetchBufferCmd), (32 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramByte), (56 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramDoubleWord), (64 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramHalfWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_ProgramWord), (60 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_SetLatency), (12 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_Unlock), (36 bytes).
    Removing stm32f4xx_flash.o(i.FLASH_WaitForLastOperation), (34 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_flash_ramfunc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashInterfaceCmd), (32 bytes).
    Removing stm32f4xx_flash_ramfunc.o(i.FLASH_FlashSleepModeCmd), (32 bytes).
    Removing stm32f4xx_fmc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ClearFlag), (60 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ClearITPendingBit), (68 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetECC), (18 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetFlagStatus), (54 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetITStatus), (80 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_GetModeStatus), (30 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_ITConfig), (120 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDCmd), (64 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDDeInit), (40 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDECCCmd), (64 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDInit), (164 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NANDStructInit), (54 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMCmd), (36 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMDeInit), (48 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMInit), (268 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_NORSRAMStructInit), (52 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDCmd), (36 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDDeInit), (28 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDInit), (120 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_PCCARDStructInit), (60 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMCmdConfig), (32 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMDeInit), (40 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMInit), (262 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMStructInit), (60 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SDRAMWriteProtectionConfig), (40 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SetAutoRefresh_Number), (16 bytes).
    Removing stm32f4xx_fmc.o(i.FMC_SetRefreshCount), (16 bytes).
    Removing stm32f4xx_fmc.o(.constdata), (28 bytes).
    Removing stm32f4xx_fmpi2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_fmpi2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_DeInit), (340 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_PinLockConfig), (26 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadInputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputData), (6 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ReadOutputDataBit), (14 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ResetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_SetBits), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_StructInit), (18 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_ToggleBits), (8 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_Write), (4 bytes).
    Removing stm32f4xx_gpio.o(i.GPIO_WriteBit), (12 bytes).
    Removing stm32f4xx_hash.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash.o(i.HASH_AutoStartDigest), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearFlag), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DMACmd), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DataIn), (12 bytes).
    Removing stm32f4xx_hash.o(i.HASH_DeInit), (22 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetDigest), (72 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetFlagStatus), (32 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetITStatus), (28 bytes).
    Removing stm32f4xx_hash.o(i.HASH_GetInFIFOWordsNbr), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_ITConfig), (28 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Init), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_Reset), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_RestoreContext), (68 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SaveContext), (60 bytes).
    Removing stm32f4xx_hash.o(i.HASH_SetLastWordValidBitsNbr), (24 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StartDigest), (16 bytes).
    Removing stm32f4xx_hash.o(i.HASH_StructInit), (12 bytes).
    Removing stm32f4xx_hash_md5.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_md5.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_md5.o(i.HASH_MD5), (156 bytes).
    Removing stm32f4xx_hash_md5.o(i.HMAC_MD5), (328 bytes).
    Removing stm32f4xx_hash_sha1.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_hash_sha1.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HASH_SHA1), (162 bytes).
    Removing stm32f4xx_hash_sha1.o(i.HMAC_SHA1), (336 bytes).
    Removing stm32f4xx_i2c.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_i2c.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ARPCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AcknowledgeConfig), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_AnalogFilterCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CalculatePEC), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_CheckEvent), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearFlag), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Cmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMACmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DMALastTransferCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DeInit), (100 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DigitalFilterConfig), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_DualAddressCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_FastModeDutyCycleConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GeneralCallCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTART), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GenerateSTOP), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetFlagStatus), (50 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetITStatus), (34 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetLastEvent), (14 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_GetPEC), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ITConfig), (20 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Init), (188 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_NACKPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_OwnAddress2Config), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_PECPositionConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReadRegister), (16 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_ReceiveData), (6 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SMBusAlertConfig), (26 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_Send7bitAddress), (18 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SendData), (4 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_SoftwareResetCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StretchClockCmd), (24 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_StructInit), (28 bytes).
    Removing stm32f4xx_i2c.o(i.I2C_TransmitPEC), (24 bytes).
    Removing stm32f4xx_iwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_iwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_Enable), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_ReloadCounter), (16 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetPrescaler), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_SetReload), (12 bytes).
    Removing stm32f4xx_iwdg.o(i.IWDG_WriteAccessCmd), (12 bytes).
    Removing stm32f4xx_ltdc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_ltdc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTCmd), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTInit), (26 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_CLUTStructInit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearFlag), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Cmd), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingConfig), (64 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ColorKeyingStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DeInit), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_DitherCmd), (32 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetCDStatus), (20 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetITStatus), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetPosStatus), (36 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_GetRGBWidth), (52 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ITConfig), (28 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_Init), (168 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LIPConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAddress), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerAlpha), (4 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerCmd), (24 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerInit), (150 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPixelFormat), (98 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerPosition), (120 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerSize), (88 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_LayerStructInit), (44 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_PosStructInit), (8 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_RGBStructInit), (10 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_ReloadConfig), (12 bytes).
    Removing stm32f4xx_ltdc.o(i.LTDC_StructInit), (34 bytes).
    Removing stm32f4xx_pwr.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_pwr.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupAccessCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_BackupRegulatorCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_ClearFlag), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_DeInit), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTANDBYMode), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_EnterUnderDriveSTOPMode), (56 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_FlashPowerDownCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_LowRegulatorUnderDriveCmd), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorModeConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_MainRegulatorUnderDriveCmd), (24 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_OverDriveSWCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDCmd), (12 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_PVDLevelConfig), (20 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_UnderDriveCmd), (32 bytes).
    Removing stm32f4xx_pwr.o(i.PWR_WakeUpPinCmd), (12 bytes).
    Removing stm32f4xx_qspi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_qspi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB1PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB2PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AHB3PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB1PeriphResetCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_APB2PeriphClockLPModeCmd), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_AdjustHSICalibrationValue), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_BackupResetCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearFlag), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ClockSecuritySystemCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_DeInit), (84 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetFlagStatus), (56 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetITStatus), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_GetSYSCLKSource), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSEConfig), (16 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_HSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_I2SCLKConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_ITConfig), (28 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSEModeConfig), (32 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LSICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_LTDCCLKDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_MCO2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK1Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PCLK2Config), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLConfig), (36 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLI2SConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAICmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_PLLSAIConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKCmd), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_RTCCLKConfig), (48 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockACLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIBlockBCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLI2SClkDivConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SAIPLLSAIClkDivConfig), (24 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_SYSCLKConfig), (20 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_TIMCLKPresConfig), (12 bytes).
    Removing stm32f4xx_rcc.o(i.RCC_WaitForHSEStartUp), (48 bytes).
    Removing stm32f4xx_rng.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rng.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rng.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearFlag), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_rng.o(i.RNG_Cmd), (32 bytes).
    Removing stm32f4xx_rng.o(i.RNG_DeInit), (22 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetITStatus), (20 bytes).
    Removing stm32f4xx_rng.o(i.RNG_GetRandomNumber), (12 bytes).
    Removing stm32f4xx_rng.o(i.RNG_ITConfig), (32 bytes).
    Removing stm32f4xx_rtc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rtc.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmCmd), (92 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmStructInit), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_AlarmSubSecondConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Bcd2ToByte), (18 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_BypassShadowCmd), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ByteToBcd2), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputCmd), (48 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CalibOutputConfig), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearFlag), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ClearITPendingBit), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibCmd), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_CoarseCalibConfig), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DateStructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DayLightSavingConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_DeInit), (184 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_EnterInitMode), (68 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ExitInitMode), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarm), (108 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetAlarmSubSecond), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetDate), (72 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetFlagStatus), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetITStatus), (56 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetStoreOperation), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetSubSecond), (16 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTime), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStamp), (136 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetTimeStampSubSecond), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_GetWakeUpCounter), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ITConfig), (68 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_Init), (80 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputConfig), (44 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_OutputTypeConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_ReadBackupRegister), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_RefClockCmd), (64 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetAlarm), (188 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetDate), (164 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetTime), (168 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SetWakeUpCounter), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SmoothCalibConfig), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_StructInit), (14 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_SynchroShiftConfig), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperCmd), (28 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperFilterConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinSelection), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPinsPrechargeDuration), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperPullUpCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperSamplingFreqConfig), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TamperTriggerConfig), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampCmd), (52 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampOnTamperDetectionCmd), (32 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStampPinSelection), (24 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_TimeStructInit), (12 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WaitForSynchro), (76 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpClockConfig), (40 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WakeUpCmd), (96 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteBackupRegister), (20 bytes).
    Removing stm32f4xx_rtc.o(i.RTC_WriteProtectionCmd), (28 bytes).
    Removing stm32f4xx_sai.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sai.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sai.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearFlag), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ClearITPendingBit), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Cmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_CompandingModeConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DMACmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_DeInit), (36 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FlushFIFO), (10 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameInit), (44 bytes).
    Removing stm32f4xx_sai.o(i.SAI_FrameStructInit), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetCmdStatus), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFIFOStatus), (8 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_GetITStatus), (22 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ITConfig), (20 bytes).
    Removing stm32f4xx_sai.o(i.SAI_Init), (68 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MonoModeConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteFrameCounterConfig), (18 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteModeCmd), (24 bytes).
    Removing stm32f4xx_sai.o(i.SAI_MuteValueConfig), (16 bytes).
    Removing stm32f4xx_sai.o(i.SAI_ReceiveData), (4 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SendData), (4 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotInit), (34 bytes).
    Removing stm32f4xx_sai.o(i.SAI_SlotStructInit), (14 bytes).
    Removing stm32f4xx_sai.o(i.SAI_StructInit), (26 bytes).
    Removing stm32f4xx_sai.o(i.SAI_TRIStateConfig), (18 bytes).
    Removing stm32f4xx_sdio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_sdio.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CEATAITCmd), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearFlag), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClearITPendingBit), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ClockCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CmdStructInit), (14 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_CommandCompletionCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DMACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataConfig), (48 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DataStructInit), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_DeInit), (24 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetCommandResponse), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetDataCounter), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFIFOCount), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetITStatus), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetPowerState), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_GetResponse), (20 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ITConfig), (28 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_Init), (44 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_ReadData), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCEATACmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendCommand), (40 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SendSDIOSuspendCmd), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetPowerState), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOOperation), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_SetSDIOReadWaitMode), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StartSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StopSDIOReadWait), (12 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_StructInit), (16 bytes).
    Removing stm32f4xx_sdio.o(i.SDIO_WriteData), (12 bytes).
    Removing stm32f4xx_spdifrx.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spdifrx.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_spi.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_spi.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.I2S_FullDuplexConfig), (58 bytes).
    Removing stm32f4xx_spi.o(i.I2S_Init), (240 bytes).
    Removing stm32f4xx_spi.o(i.I2S_StructInit), (18 bytes).
    Removing stm32f4xx_spi.o(i.SPI_BiDirectionalLineConfig), (26 bytes).
    Removing stm32f4xx_spi.o(i.SPI_CalculateCRC), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Cmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_DataSizeConfig), (16 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRC), (12 bytes).
    Removing stm32f4xx_spi.o(i.SPI_GetCRCPolynomial), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearFlag), (6 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ClearITPendingBit), (14 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DMACmd), (20 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_DeInit), (196 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_GetITStatus), (44 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ITConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_ReceiveData), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_I2S_SendData), (4 bytes).
    Removing stm32f4xx_spi.o(i.SPI_Init), (56 bytes).
    Removing stm32f4xx_spi.o(i.SPI_NSSInternalSoftwareConfig), (28 bytes).
    Removing stm32f4xx_spi.o(i.SPI_SSOutputCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_StructInit), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TIModeCmd), (24 bytes).
    Removing stm32f4xx_spi.o(i.SPI_TransmitCRC), (10 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_CompensationCellCmd), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_DeInit), (24 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_ETH_MediaInterfaceConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_EXTILineConfig), (44 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_GetCompensationCellStatus), (20 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemoryRemapConfig), (12 bytes).
    Removing stm32f4xx_syscfg.o(i.SYSCFG_MemorySwappingBank), (12 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_tim.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_tim.o(i.TI1_Config), (46 bytes).
    Removing stm32f4xx_tim.o(i.TI2_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TI3_Config), (50 bytes).
    Removing stm32f4xx_tim.o(i.TI4_Config), (54 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ARRPreloadConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRConfig), (34 bytes).
    Removing stm32f4xx_tim.o(i.TIM_BDTRStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCPreloadControl), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CCxNCmd), (22 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearFlag), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearITPendingBit), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC1Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC2Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC3Ref), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ClearOC4Ref), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CounterModeConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_CtrlPWMOutputs), (28 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMACmd), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DMAConfig), (8 bytes).
    Removing stm32f4xx_tim.o(i.TIM_DeInit), (428 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode1Config), (32 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRClockMode2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ETRConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_EncoderInterfaceConfig), (50 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC1Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC2Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC3Config), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ForcedOC4Config), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GenerateEvent), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCapture4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetITStatus), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_GetPrescaler), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICInit), (98 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ICStructInit), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_ITRxExternalClockConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_InternalClockConfig), (10 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1Init), (104 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1NPolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PolarityConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC1PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2Init), (136 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC2PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3FastConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3Init), (132 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3NPolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC3PreloadConfig), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4FastConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4Init), (100 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PolarityConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OC4PreloadConfig), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_OCStructInit), (20 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PWMIConfig), (110 bytes).
    Removing stm32f4xx_tim.o(i.TIM_PrescalerConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_RemapConfig), (6 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCCDMA), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectCOM), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectHallSensor), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectInputTrigger), (12 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectMasterSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOCxM), (80 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectOnePulseMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SelectSlaveMode), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetAutoreload), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetClockDivision), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare1), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare2), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare3), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCompare4), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetCounter), (4 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC1Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC2Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC3Prescaler), (16 bytes).
    Removing stm32f4xx_tim.o(i.TIM_SetIC4Prescaler), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TIxExternalClockConfig), (48 bytes).
    Removing stm32f4xx_tim.o(i.TIM_TimeBaseStructInit), (18 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateDisableConfig), (24 bytes).
    Removing stm32f4xx_tim.o(i.TIM_UpdateRequestConfig), (24 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearFlag), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClearITPendingBit), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockInit), (28 bytes).
    Removing stm32f4xx_usart.o(i.USART_ClockStructInit), (12 bytes).
    Removing stm32f4xx_usart.o(i.USART_DMACmd), (20 bytes).
    Removing stm32f4xx_usart.o(i.USART_DeInit), (256 bytes).
    Removing stm32f4xx_usart.o(i.USART_GetFlagStatus), (14 bytes).
    Removing stm32f4xx_usart.o(i.USART_HalfDuplexCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDACmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_IrDAConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINBreakDetectLengthConfig), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_LINCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OneBitMethodCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_OverSampling8Cmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_ReceiverWakeUpCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendBreak), (10 bytes).
    Removing stm32f4xx_usart.o(i.USART_SendData), (8 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetAddress), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetGuardTime), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SetPrescaler), (16 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_SmartCardNACKCmd), (24 bytes).
    Removing stm32f4xx_usart.o(i.USART_StructInit), (22 bytes).
    Removing stm32f4xx_usart.o(i.USART_WakeUpConfig), (16 bytes).
    Removing stm32f4xx_wwdg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_wwdg.o(.rrx_text), (6 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_ClearFlag), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_DeInit), (24 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_Enable), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_EnableIT), (12 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_GetFlagStatus), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetCounter), (16 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetPrescaler), (20 bytes).
    Removing stm32f4xx_wwdg.o(i.WWDG_SetWindowValue), (32 bytes).
    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.rrx_text), (6 bytes).
    Removing main.o(.bss), (980 bytes).
    Removing main.o(.bss), (980 bytes).
    Removing main.o(.bss), (560 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (2 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (1 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (8 bytes).
    Removing main.o(.data), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_it.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(.rev16_text), (4 bytes).
    Removing bsp_led.o(.revsh_text), (4 bytes).
    Removing bsp_led.o(.rrx_text), (6 bytes).
    Removing bsp_led.o(i.LED_GPIO_Config), (92 bytes).
    Removing bsp_systick.o(.rev16_text), (4 bytes).
    Removing bsp_systick.o(.revsh_text), (4 bytes).
    Removing bsp_systick.o(.rrx_text), (6 bytes).
    Removing bsp_systick.o(i.Delay_us), (20 bytes).
    Removing ad9833.o(.rev16_text), (4 bytes).
    Removing ad9833.o(.revsh_text), (4 bytes).
    Removing ad9833.o(.rrx_text), (6 bytes).
    Removing ad9833.o(i.AD9833_ClearReset), (6 bytes).
    Removing ad9833.o(i.AD9833_Init), (18 bytes).
    Removing ad9833.o(i.AD9833_Reset), (22 bytes).
    Removing ad9833.o(i.AD9833_SPI_Write), (108 bytes).
    Removing ad9833.o(i.AD9833_SetFrequency), (80 bytes).
    Removing ad9833.o(i.AD9833_SetFrequencyQuick), (10 bytes).
    Removing ad9833.o(i.AD9833_SetPhase), (6 bytes).
    Removing ad9833.o(i.AD9833_SetRegisterValue), (36 bytes).
    Removing ad9833.o(i.AD9833_SetWave), (4 bytes).
    Removing ad9833.o(i.AD9833_Setup), (8 bytes).
    Removing ad9833.o(i.AD983_GPIO_Init), (48 bytes).
    Removing ad9833.o(.constdata), (5 bytes).
    Removing ad9833_2.o(.rev16_text), (4 bytes).
    Removing ad9833_2.o(.revsh_text), (4 bytes).
    Removing ad9833_2.o(.rrx_text), (6 bytes).
    Removing ad9833_2.o(i.AD9833_2_ClearReset), (6 bytes).
    Removing ad9833_2.o(i.AD9833_2_Init), (18 bytes).
    Removing ad9833_2.o(i.AD9833_2_Reset), (22 bytes).
    Removing ad9833_2.o(i.AD9833_2_SPI_Write), (108 bytes).
    Removing ad9833_2.o(i.AD9833_2_SetFrequency), (80 bytes).
    Removing ad9833_2.o(i.AD9833_2_SetFrequencyQuick), (10 bytes).
    Removing ad9833_2.o(i.AD9833_2_SetPhase), (6 bytes).
    Removing ad9833_2.o(i.AD9833_2_SetRegisterValue), (36 bytes).
    Removing ad9833_2.o(i.AD9833_2_SetWave), (4 bytes).
    Removing ad9833_2.o(i.AD9833_2_Setup), (8 bytes).
    Removing ad9833_2.o(i.AD983_2_GPIO_Init), (48 bytes).
    Removing ad9833_2.o(i.Delay_2), (18 bytes).
    Removing ad9833_2.o(.constdata), (5 bytes).
    Removing ad9959.o(.rev16_text), (4 bytes).
    Removing ad9959.o(.revsh_text), (4 bytes).
    Removing ad9959.o(.rrx_text), (6 bytes).
    Removing ad9959.o(i.AD9959_Init), (112 bytes).
    Removing ad9959.o(i.AD9959_Modulation_Init), (136 bytes).
    Removing ad9959.o(i.AD9959_SetASK), (66 bytes).
    Removing ad9959.o(i.AD9959_SetAmp_Sweep), (98 bytes).
    Removing ad9959.o(i.AD9959_SetFSK), (58 bytes).
    Removing ad9959.o(i.AD9959_SetFre_Sweep), (128 bytes).
    Removing ad9959.o(i.AD9959_SetPSK), (58 bytes).
    Removing ad9959.o(i.AD9959_SetPhase_Sweep), (82 bytes).
    Removing ad9959.o(i.AD9959_Set_Amp), (28 bytes).
    Removing ad9959.o(i.AD9959_Set_Fre), (28 bytes).
    Removing ad9959.o(i.AD9959_Set_Phase), (28 bytes).
    Removing ad9959.o(i.AD9959_WriteData), (124 bytes).
    Removing ad9959.o(i.IO_Update), (32 bytes).
    Removing ad9959.o(i.IntReset), (32 bytes).
    Removing ad9959.o(i.Intserve), (44 bytes).
    Removing ad9959.o(i.Write_ACR), (32 bytes).
    Removing ad9959.o(i.Write_CFTW0), (76 bytes).
    Removing ad9959.o(i.Write_CPOW0), (28 bytes).
    Removing ad9959.o(i.Write_FDW), (40 bytes).
    Removing ad9959.o(i.Write_LSRR), (26 bytes).
    Removing ad9959.o(i.Write_Profile_Ampli), (32 bytes).
    Removing ad9959.o(i.Write_Profile_Fre), (80 bytes).
    Removing ad9959.o(i.Write_Profile_Phase), (32 bytes).
    Removing ad9959.o(i.Write_RDW), (40 bytes).
    Removing ad9959.o(i.delay1), (14 bytes).
    Removing ad9959.o(.data), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.rrx_text), (6 bytes).
    Removing usart.o(i._sys_exit), (2 bytes).
    Removing ad9226.o(.rev16_text), (4 bytes).
    Removing ad9226.o(.revsh_text), (4 bytes).
    Removing ad9226.o(.rrx_text), (6 bytes).
    Removing ad9226.o(i.AD9226_Init), (120 bytes).
    Removing ad9226.o(i.ReadAD9226_Data), (116 bytes).
    Removing bsp_debug_usart.o(.rev16_text), (4 bytes).
    Removing bsp_debug_usart.o(.revsh_text), (4 bytes).
    Removing bsp_debug_usart.o(.rrx_text), (6 bytes).
    Removing bsp_debug_usart.o(i.Debug_USART_Config), (140 bytes).
    Removing adc.o(.rev16_text), (4 bytes).
    Removing adc.o(.revsh_text), (4 bytes).
    Removing adc.o(.rrx_text), (6 bytes).
    Removing adc.o(i.QCZ_FFT1), (184 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing adc.o(.data), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.rrx_text), (6 bytes).
    Removing timer.o(i.TIM4_Int_Init), (92 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing fft.o(.rev16_text), (4 bytes).
    Removing fft.o(.revsh_text), (4 bytes).
    Removing fft.o(.rrx_text), (6 bytes).
    Removing fft.o(i.Get_basevpp_point), (64 bytes).
    Removing fft.o(i.Get_othervpp_point), (30 bytes).
    Removing fft.o(i.Get_vpp_fre), (272 bytes).
    Removing fft.o(i.get_basefrevpp), (232 bytes).
    Removing fft.o(i.get_pianyik), (88 bytes).
    Removing fft.o(i.n_get_vppfre), (164 bytes).
    Removing fft.o(.bss), (20 bytes).
    Removing fft.o(.bss), (20 bytes).
    Removing fft.o(.bss), (20 bytes).
    Removing fft.o(.bss), (80 bytes).
    Removing fft.o(.bss), (24 bytes).
    Removing fft.o(.bss), (8172 bytes).
    Removing fft.o(.bss), (8172 bytes).
    Removing fft.o(.data), (4 bytes).
    Removing fft.o(.data), (1 bytes).
    Removing fft.o(.data), (1 bytes).
    Removing fft.o(.data), (4 bytes).
    Removing fft.o(.data), (4 bytes).
    Removing fft.o(.data), (1 bytes).
    Removing arm_cmplx_mag_f32.o(.rev16_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.revsh_text), (4 bytes).
    Removing arm_cmplx_mag_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_radix4_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_f32.o(.rrx_text), (6 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rev16_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.revsh_text), (4 bytes).
    Removing arm_cfft_radix4_init_f32.o(.rrx_text), (6 bytes).
    Removing arm_bitreversal.o(.rev16_text), (4 bytes).
    Removing arm_bitreversal.o(.revsh_text), (4 bytes).
    Removing arm_bitreversal.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.rev16_text), (4 bytes).
    Removing arm_common_tables.o(.revsh_text), (4 bytes).
    Removing arm_common_tables.o(.rrx_text), (6 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (24576 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (192 bytes).
    Removing arm_common_tables.o(.constdata), (384 bytes).
    Removing arm_common_tables.o(.constdata), (768 bytes).
    Removing arm_common_tables.o(.constdata), (1536 bytes).
    Removing arm_common_tables.o(.constdata), (3072 bytes).
    Removing arm_common_tables.o(.constdata), (6144 bytes).
    Removing arm_common_tables.o(.constdata), (12288 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (40 bytes).
    Removing arm_common_tables.o(.constdata), (96 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (416 bytes).
    Removing arm_common_tables.o(.constdata), (880 bytes).
    Removing arm_common_tables.o(.constdata), (896 bytes).
    Removing arm_common_tables.o(.constdata), (3600 bytes).
    Removing arm_common_tables.o(.constdata), (7616 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (24 bytes).
    Removing arm_common_tables.o(.constdata), (48 bytes).
    Removing arm_common_tables.o(.constdata), (112 bytes).
    Removing arm_common_tables.o(.constdata), (224 bytes).
    Removing arm_common_tables.o(.constdata), (480 bytes).
    Removing arm_common_tables.o(.constdata), (960 bytes).
    Removing arm_common_tables.o(.constdata), (1984 bytes).
    Removing arm_common_tables.o(.constdata), (3968 bytes).
    Removing arm_common_tables.o(.constdata), (8064 bytes).
    Removing arm_common_tables.o(.constdata), (128 bytes).
    Removing arm_common_tables.o(.constdata), (256 bytes).
    Removing arm_common_tables.o(.constdata), (512 bytes).
    Removing arm_common_tables.o(.constdata), (1024 bytes).
    Removing arm_common_tables.o(.constdata), (2048 bytes).
    Removing arm_common_tables.o(.constdata), (4096 bytes).
    Removing arm_common_tables.o(.constdata), (8192 bytes).
    Removing arm_common_tables.o(.constdata), (16384 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (2052 bytes).
    Removing arm_common_tables.o(.constdata), (1026 bytes).
    Removing dfixui.o(.text), (50 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing dfixul.o(.text), (48 bytes).
    Removing cdrcmple.o(.text), (48 bytes).

1037 unused section(s) (total 235440 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpyb.o ABSOLUTE
    ../clib/microlib/string/memcpy.c         0x00000000   Number         0  memcpya.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixui.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixi.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dfltui.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos.o ABSOLUTE
    ../mathlib/cos.c                         0x00000000   Number         0  cos_x.o ABSOLUTE
    ../mathlib/cos_i.c                       0x00000000   Number         0  cos_i.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/rred.c                        0x00000000   Number         0  rred.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin.o ABSOLUTE
    ../mathlib/sin.c                         0x00000000   Number         0  sin_x.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i.o ABSOLUTE
    ../mathlib/sin_i.c                       0x00000000   Number         0  sin_i_x.o ABSOLUTE
    ..\..\Libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\arm\startup_stm32f429_439xx.s 0x00000000   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    ..\..\Libraries\CMSIS\Device\ST\STM32F4xx\Source\Templates\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cec.c 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fmc.c 0x00000000   Number         0  stm32f4xx_fmc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_fmpi2c.c 0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_qspi.c 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spdifrx.c 0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\..\Libraries\STM32F4xx_StdPeriph_Driver\src\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\..\Source\CommonTables\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\..\Source\ComplexMathFunctions\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\..\Source\TransformFunctions\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\..\User\AD9226\ad9226.c               0x00000000   Number         0  ad9226.o ABSOLUTE
    ..\..\User\AD9833\AD9833.c               0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\..\User\AD9833_2\AD9833_2.c           0x00000000   Number         0  ad9833_2.o ABSOLUTE
    ..\..\User\AD9959\AD9959.c               0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\..\User\ADC\adc.c                     0x00000000   Number         0  adc.o ABSOLUTE
    ..\..\User\FFT\fft.c                     0x00000000   Number         0  fft.o ABSOLUTE
    ..\..\User\TIMER2\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\..\User\led\bsp_led.c                 0x00000000   Number         0  bsp_led.o ABSOLUTE
    ..\..\User\main.c                        0x00000000   Number         0  main.o ABSOLUTE
    ..\..\User\stm32f4xx_it.c                0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\..\User\systick\bsp_SysTick.c         0x00000000   Number         0  bsp_systick.o ABSOLUTE
    ..\..\User\usart2\usart.c                0x00000000   Number         0  usart.o ABSOLUTE
    ..\..\User\usart\bsp_debug_usart.c       0x00000000   Number         0  bsp_debug_usart.o ABSOLUTE
    ..\\..\\Libraries\\CMSIS\\Device\\ST\\STM32F4xx\\Source\\Templates\\system_stm32f4xx.c 0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\misc.c 0x00000000   Number         0  misc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_adc.c 0x00000000   Number         0  stm32f4xx_adc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_can.c 0x00000000   Number         0  stm32f4xx_can.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cec.c 0x00000000   Number         0  stm32f4xx_cec.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_crc.c 0x00000000   Number         0  stm32f4xx_crc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp.c 0x00000000   Number         0  stm32f4xx_cryp.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_aes.c 0x00000000   Number         0  stm32f4xx_cryp_aes.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_des.c 0x00000000   Number         0  stm32f4xx_cryp_des.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_cryp_tdes.c 0x00000000   Number         0  stm32f4xx_cryp_tdes.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dac.c 0x00000000   Number         0  stm32f4xx_dac.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dbgmcu.c 0x00000000   Number         0  stm32f4xx_dbgmcu.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dcmi.c 0x00000000   Number         0  stm32f4xx_dcmi.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma.c 0x00000000   Number         0  stm32f4xx_dma.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_dma2d.c 0x00000000   Number         0  stm32f4xx_dma2d.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_exti.c 0x00000000   Number         0  stm32f4xx_exti.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash.c 0x00000000   Number         0  stm32f4xx_flash.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_flash_ramfunc.c 0x00000000   Number         0  stm32f4xx_flash_ramfunc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fmc.c 0x00000000   Number         0  stm32f4xx_fmc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_fmpi2c.c 0x00000000   Number         0  stm32f4xx_fmpi2c.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_gpio.c 0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash.c 0x00000000   Number         0  stm32f4xx_hash.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_md5.c 0x00000000   Number         0  stm32f4xx_hash_md5.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_hash_sha1.c 0x00000000   Number         0  stm32f4xx_hash_sha1.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_i2c.c 0x00000000   Number         0  stm32f4xx_i2c.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_iwdg.c 0x00000000   Number         0  stm32f4xx_iwdg.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_ltdc.c 0x00000000   Number         0  stm32f4xx_ltdc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_pwr.c 0x00000000   Number         0  stm32f4xx_pwr.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_qspi.c 0x00000000   Number         0  stm32f4xx_qspi.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rcc.c 0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rng.c 0x00000000   Number         0  stm32f4xx_rng.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_rtc.c 0x00000000   Number         0  stm32f4xx_rtc.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sai.c 0x00000000   Number         0  stm32f4xx_sai.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_sdio.c 0x00000000   Number         0  stm32f4xx_sdio.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spdifrx.c 0x00000000   Number         0  stm32f4xx_spdifrx.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_spi.c 0x00000000   Number         0  stm32f4xx_spi.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_syscfg.c 0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_tim.c 0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_usart.c 0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\..\\Libraries\\STM32F4xx_StdPeriph_Driver\\src\\stm32f4xx_wwdg.c 0x00000000   Number         0  stm32f4xx_wwdg.o ABSOLUTE
    ..\\..\\Source\\CommonTables\\arm_common_tables.c 0x00000000   Number         0  arm_common_tables.o ABSOLUTE
    ..\\..\\Source\\ComplexMathFunctions\\arm_cmplx_mag_f32.c 0x00000000   Number         0  arm_cmplx_mag_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_bitreversal.c 0x00000000   Number         0  arm_bitreversal.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix4_f32.c 0x00000000   Number         0  arm_cfft_radix4_f32.o ABSOLUTE
    ..\\..\\Source\\TransformFunctions\\arm_cfft_radix4_init_f32.c 0x00000000   Number         0  arm_cfft_radix4_init_f32.o ABSOLUTE
    ..\\..\\User\\AD9226\\ad9226.c           0x00000000   Number         0  ad9226.o ABSOLUTE
    ..\\..\\User\\AD9833\\AD9833.c           0x00000000   Number         0  ad9833.o ABSOLUTE
    ..\\..\\User\\AD9833_2\\AD9833_2.c       0x00000000   Number         0  ad9833_2.o ABSOLUTE
    ..\\..\\User\\AD9959\\AD9959.c           0x00000000   Number         0  ad9959.o ABSOLUTE
    ..\\..\\User\\ADC\\adc.c                 0x00000000   Number         0  adc.o ABSOLUTE
    ..\\..\\User\\FFT\\fft.c                 0x00000000   Number         0  fft.o ABSOLUTE
    ..\\..\\User\\TIMER2\\timer.c            0x00000000   Number         0  timer.o ABSOLUTE
    ..\\..\\User\\led\\bsp_led.c             0x00000000   Number         0  bsp_led.o ABSOLUTE
    ..\\..\\User\\main.c                     0x00000000   Number         0  main.o ABSOLUTE
    ..\\..\\User\\stm32f4xx_it.c             0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    ..\\..\\User\\systick\\bsp_SysTick.c     0x00000000   Number         0  bsp_systick.o ABSOLUTE
    ..\\..\\User\\usart2\\usart.c            0x00000000   Number         0  usart.o ABSOLUTE
    ..\\..\\User\\usart\\bsp_debug_usart.c   0x00000000   Number         0  bsp_debug_usart.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    RESET                                    0x08000000   Section      428  startup_stm32f429_439xx.o(RESET)
    .ARM.Collect$$$$00000000                 0x080001ac   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x080001ac   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x080001b0   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x080001b4   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x080001b4   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x080001b4   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000D                 0x080001bc   Section        0  entry10a.o(.ARM.Collect$$$$0000000D)
    .ARM.Collect$$$$0000000F                 0x080001bc   Section        0  entry11a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00002712                 0x080001bc   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x080001bc   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001c0   Section       36  startup_stm32f429_439xx.o(.text)
    $v0                                      0x080001c0   Number         0  startup_stm32f429_439xx.o(.text)
    .text                                    0x080001e4   Section        0  arm_cmplx_mag_f32.o(.text)
    .text                                    0x080002e0   Section        0  arm_cfft_radix4_f32.o(.text)
    .text                                    0x08000980   Section        0  arm_cfft_radix4_init_f32.o(.text)
    .text                                    0x08000a40   Section        0  arm_bitreversal.o(.text)
    .text                                    0x08000c16   Section        0  dadd.o(.text)
    .text                                    0x08000d64   Section        0  dmul.o(.text)
    .text                                    0x08000e48   Section        0  ddiv.o(.text)
    .text                                    0x08000f26   Section        0  dfltui.o(.text)
    .text                                    0x08000f40   Section        0  f2d.o(.text)
    .text                                    0x08000f66   Section        0  d2f.o(.text)
    .text                                    0x08000f9e   Section        0  uidiv.o(.text)
    .text                                    0x08000fca   Section        0  llshl.o(.text)
    .text                                    0x08000fe8   Section        0  llushr.o(.text)
    .text                                    0x08001008   Section        0  llsshr.o(.text)
    .text                                    0x0800102c   Section        0  fepilogue.o(.text)
    .text                                    0x0800102c   Section        0  iusefp.o(.text)
    .text                                    0x0800109a   Section        0  depilogue.o(.text)
    .text                                    0x08001154   Section       36  init.o(.text)
    .text                                    0x08001178   Section        0  dflti.o(.text)
    .text                                    0x0800119a   Section        0  dfixi.o(.text)
    i.ADC_Cmd                                0x080011d8   Section        0  stm32f4xx_adc.o(i.ADC_Cmd)
    i.ADC_CommonInit                         0x080011f0   Section        0  stm32f4xx_adc.o(i.ADC_CommonInit)
    i.ADC_DMACmd                             0x08001218   Section        0  stm32f4xx_adc.o(i.ADC_DMACmd)
    i.ADC_DMARequestAfterLastTransferCmd     0x08001230   Section        0  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    i.ADC_DiscModeChannelCountConfig         0x08001248   Section        0  stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig)
    i.ADC_DiscModeCmd                        0x08001258   Section        0  stm32f4xx_adc.o(i.ADC_DiscModeCmd)
    i.ADC_Init                               0x08001270   Section        0  stm32f4xx_adc.o(i.ADC_Init)
    i.ADC_RegularChannelConfig               0x080012bc   Section        0  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    i.Adc2_Init                              0x08001330   Section        0  adc.o(i.Adc2_Init)
    i.Adc3_Init                              0x080013ec   Section        0  adc.o(i.Adc3_Init)
    i.Adc_Init                               0x080014a8   Section        0  adc.o(i.Adc_Init)
    i.BusFault_Handler                       0x08001560   Section        0  stm32f4xx_it.o(i.BusFault_Handler)
    i.DMA1_Init                              0x08001564   Section        0  adc.o(i.DMA1_Init)
    i.DMA2_Init                              0x08001618   Section        0  adc.o(i.DMA2_Init)
    i.DMA2_Stream0_IRQHandler                0x080016d0   Section        0  adc.o(i.DMA2_Stream0_IRQHandler)
    i.DMA2_Stream1_IRQHandler                0x08001710   Section        0  adc.o(i.DMA2_Stream1_IRQHandler)
    i.DMA2_Stream2_IRQHandler                0x08001750   Section        0  adc.o(i.DMA2_Stream2_IRQHandler)
    i.DMA3_Init                              0x08001790   Section        0  adc.o(i.DMA3_Init)
    i.DMA_ClearITPendingBit                  0x08001848   Section        0  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    i.DMA_Cmd                                0x08001874   Section        0  stm32f4xx_dma.o(i.DMA_Cmd)
    i.DMA_DeInit                             0x0800188c   Section        0  stm32f4xx_dma.o(i.DMA_DeInit)
    i.DMA_GetCmdStatus                       0x080019b8   Section        0  stm32f4xx_dma.o(i.DMA_GetCmdStatus)
    i.DMA_GetITStatus                        0x080019c8   Section        0  stm32f4xx_dma.o(i.DMA_GetITStatus)
    i.DMA_ITConfig                           0x08001a1c   Section        0  stm32f4xx_dma.o(i.DMA_ITConfig)
    i.DMA_Init                               0x08001a50   Section        0  stm32f4xx_dma.o(i.DMA_Init)
    i.DebugMon_Handler                       0x08001aa4   Section        0  stm32f4xx_it.o(i.DebugMon_Handler)
    i.FFT                                    0x08001aa8   Section        0  fft.o(i.FFT)
    i.GPIO_Init                              0x08001b3c   Section        0  stm32f4xx_gpio.o(i.GPIO_Init)
    i.GPIO_PinAFConfig                       0x08001bb8   Section        0  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    i.Hanningwindow                          0x08001bd8   Section        0  fft.o(i.Hanningwindow)
    i.HardFault_Handler                      0x08001c40   Section        0  stm32f4xx_it.o(i.HardFault_Handler)
    i.MemManage_Handler                      0x08001c42   Section        0  stm32f4xx_it.o(i.MemManage_Handler)
    i.NMI_Handler                            0x08001c44   Section        0  stm32f4xx_it.o(i.NMI_Handler)
    i.NVIC_Init                              0x08001c48   Section        0  misc.o(i.NVIC_Init)
    i.NVIC_PriorityGroupConfig               0x08001cb0   Section        0  misc.o(i.NVIC_PriorityGroupConfig)
    i.PendSV_Handler                         0x08001cc4   Section        0  stm32f4xx_it.o(i.PendSV_Handler)
    i.QCZ_FFT                                0x08001cc8   Section        0  adc.o(i.QCZ_FFT)
    i.RCC_AHB1PeriphClockCmd                 0x08001dc8   Section        0  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    i.RCC_APB1PeriphClockCmd                 0x08001de4   Section        0  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    i.RCC_APB2PeriphClockCmd                 0x08001e00   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    i.RCC_APB2PeriphResetCmd                 0x08001e1c   Section        0  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    i.RCC_GetClocksFreq                      0x08001e38   Section        0  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    i.SVC_Handler                            0x08001edc   Section        0  stm32f4xx_it.o(i.SVC_Handler)
    i.SetSysClock                            0x08001ee0   Section        0  system_stm32f4xx.o(i.SetSysClock)
    SetSysClock                              0x08001ee1   Thumb Code   196  system_stm32f4xx.o(i.SetSysClock)
    i.SysTick_Handler                        0x08001fb4   Section        0  stm32f4xx_it.o(i.SysTick_Handler)
    i.SysTick_Init                           0x08001fb8   Section        0  bsp_systick.o(i.SysTick_Init)
    i.SystemInit                             0x08001ff8   Section        0  system_stm32f4xx.o(i.SystemInit)
    i.TIM3_Int_Init                          0x08002054   Section        0  timer.o(i.TIM3_Int_Init)
    i.TIM_Cmd                                0x0800208c   Section        0  stm32f4xx_tim.o(i.TIM_Cmd)
    i.TIM_SelectOutputTrigger                0x080020a4   Section        0  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    i.TIM_TimeBaseInit                       0x080020b4   Section        0  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    i.TimingDelay_Decrement                  0x08002130   Section        0  bsp_systick.o(i.TimingDelay_Decrement)
    i.USART1_IRQHandler                      0x08002144   Section        0  usart.o(i.USART1_IRQHandler)
    i.USART_Cmd                              0x080021ac   Section        0  stm32f4xx_usart.o(i.USART_Cmd)
    i.USART_GetITStatus                      0x080021c4   Section        0  stm32f4xx_usart.o(i.USART_GetITStatus)
    i.USART_ITConfig                         0x08002204   Section        0  stm32f4xx_usart.o(i.USART_ITConfig)
    i.USART_Init                             0x0800223c   Section        0  stm32f4xx_usart.o(i.USART_Init)
    i.USART_ReceiveData                      0x080022f8   Section        0  stm32f4xx_usart.o(i.USART_ReceiveData)
    i.UsageFault_Handler                     0x08002300   Section        0  stm32f4xx_it.o(i.UsageFault_Handler)
    i.__0printf$1                            0x08002304   Section        0  printf1.o(i.__0printf$1)
    i.__ARM_fpclassify                       0x08002324   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i.__hardfp_atan                          0x08002358   Section        0  atan.o(i.__hardfp_atan)
    i.__hardfp_atan2                         0x08002630   Section        0  atan2.o(i.__hardfp_atan2)
    i.__hardfp_cos                           0x08002820   Section        0  cos.o(i.__hardfp_cos)
    i.__ieee754_rem_pio2                     0x080028e8   Section        0  rred.o(i.__ieee754_rem_pio2)
    i.__kernel_cos                           0x08002d20   Section        0  cos_i.o(i.__kernel_cos)
    i.__kernel_poly                          0x08002e90   Section        0  poly.o(i.__kernel_poly)
    i.__kernel_sin                           0x08002f88   Section        0  sin_i.o(i.__kernel_sin)
    i.__mathlib_dbl_infnan                   0x080030b8   Section        0  dunder.o(i.__mathlib_dbl_infnan)
    i.__mathlib_dbl_infnan2                  0x080030cc   Section        0  dunder.o(i.__mathlib_dbl_infnan2)
    i.__mathlib_dbl_invalid                  0x080030e0   Section        0  dunder.o(i.__mathlib_dbl_invalid)
    i.__mathlib_dbl_underflow                0x08003100   Section        0  dunder.o(i.__mathlib_dbl_underflow)
    i.__scatterload_copy                     0x08003120   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x0800312e   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003130   Section       14  handlers.o(i.__scatterload_zeroinit)
    i.__set_errno                            0x08003140   Section        0  errno.o(i.__set_errno)
    i._printf_core                           0x0800314c   Section        0  printf1.o(i._printf_core)
    _printf_core                             0x0800314d   Thumb Code   336  printf1.o(i._printf_core)
    i.atan                                   0x080032a0   Section        0  atan.o(i.atan)
    i.fabs                                   0x080032b0   Section        0  fabs.o(i.fabs)
    i.find_peak_indices                      0x080032c8   Section        0  fft.o(i.find_peak_indices)
    i.fputc                                  0x08003348   Section        0  usart.o(i.fputc)
    i.main                                   0x0800335c   Section        0  main.o(i.main)
    i.uart_init                              0x080034a8   Section        0  usart.o(i.uart_init)
    .constdata                               0x08003554   Section     2048  arm_common_tables.o(.constdata)
    .constdata                               0x08003d54   Section    32768  arm_common_tables.o(.constdata)
    .constdata                               0x0800bd58   Section      152  atan.o(.constdata)
    atanhi                                   0x0800bd58   Data          32  atan.o(.constdata)
    atanlo                                   0x0800bd78   Data          32  atan.o(.constdata)
    aTodd                                    0x0800bd98   Data          40  atan.o(.constdata)
    aTeven                                   0x0800bdc0   Data          48  atan.o(.constdata)
    .constdata                               0x0800bdf0   Section       48  cos_i.o(.constdata)
    C                                        0x0800bdf0   Data          48  cos_i.o(.constdata)
    .constdata                               0x0800be20   Section        8  qnan.o(.constdata)
    .constdata                               0x0800be28   Section      200  rred.o(.constdata)
    pio2s                                    0x0800be28   Data          48  rred.o(.constdata)
    twooverpi                                0x0800be58   Data         152  rred.o(.constdata)
    .constdata                               0x0800bef0   Section       40  sin_i.o(.constdata)
    S                                        0x0800bef0   Data          40  sin_i.o(.constdata)
    .data                                    0x20000000   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x20000014   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x20000014   Data          16  stm32f4xx_rcc.o(.data)
    .data                                    0x20000024   Section       12  main.o(.data)
    .data                                    0x20000030   Section        4  bsp_systick.o(.data)
    TimingDelay                              0x20000030   Data           4  bsp_systick.o(.data)
    .data                                    0x20000034   Section        8  usart.o(.data)
    .data                                    0x2000003c   Section        4  usart.o(.data)
    .data                                    0x20000040   Section       16  adc.o(.data)
    .data                                    0x20000050   Section       36  fft.o(.data)
    i                                        0x20000050   Data           2  fft.o(.data)
    .data                                    0x20000074   Section        4  errno.o(.data)
    _errno                                   0x20000074   Data           4  errno.o(.data)
    .bss                                     0x20000078   Section      200  usart.o(.bss)
    .bss                                     0x20000140   Section    24576  adc.o(.bss)
    .bss                                     0x20006140   Section    49172  fft.o(.bss)
    STACK                                    0x20012158   Section     1024  startup_stm32f429_439xx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x000001ac   Number         0  startup_stm32f429_439xx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f429_439xx.o(RESET)
    __Vectors_End                            0x080001ac   Data           0  startup_stm32f429_439xx.o(RESET)
    __main                                   0x080001ad   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x080001ad   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x080001b1   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x080001b5   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x080001b5   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x080001b5   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x080001b5   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_final_cpp                           0x080001bd   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000D)
    __rt_final_exit                          0x080001bd   Thumb Code     0  entry11a.o(.ARM.Collect$$$$0000000F)
    Reset_Handler                            0x080001c1   Thumb Code     8  startup_stm32f429_439xx.o(.text)
    ADC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN1_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX0_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_RX1_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_SCE_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CAN2_TX_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    CRYP_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DCMI_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream0_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream1_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream2_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA1_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2D_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream3_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream4_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream5_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream6_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    DMA2_Stream7_IRQHandler                  0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    ETH_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI0_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI15_10_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI1_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI2_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI3_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    EXTI9_5_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FLASH_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FMC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    FPU_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    HASH_RNG_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C1_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C2_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    I2C3_EV_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_ER_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    LTDC_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_FS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    PVD_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RCC_IRQHandler                           0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_Alarm_IRQHandler                     0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    RTC_WKUP_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SAI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SDIO_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI1_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    SPI6_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TAMP_STAMP_IRQHandler                    0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM2_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM3_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM4_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM5_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM6_DAC_IRQHandler                      0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM7_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_CC_IRQHandler                       0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART4_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART5_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART7_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    UART8_IRQHandler                         0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART2_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART3_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    USART6_IRQHandler                        0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    WWDG_IRQHandler                          0x080001db   Thumb Code     0  startup_stm32f429_439xx.o(.text)
    arm_cmplx_mag_f32                        0x080001e5   Thumb Code   246  arm_cmplx_mag_f32.o(.text)
    arm_radix4_butterfly_f32                 0x080002e1   Thumb Code   800  arm_cfft_radix4_f32.o(.text)
    arm_radix4_butterfly_inverse_f32         0x08000601   Thumb Code   836  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_f32                      0x08000945   Thumb Code    60  arm_cfft_radix4_f32.o(.text)
    arm_cfft_radix4_init_f32                 0x08000981   Thumb Code   146  arm_cfft_radix4_init_f32.o(.text)
    arm_bitreversal_f32                      0x08000a41   Thumb Code   178  arm_bitreversal.o(.text)
    arm_bitreversal_q31                      0x08000af3   Thumb Code   170  arm_bitreversal.o(.text)
    arm_bitreversal_q15                      0x08000b9d   Thumb Code   122  arm_bitreversal.o(.text)
    __aeabi_dadd                             0x08000c17   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x08000d59   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x08000d5f   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x08000d65   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x08000e49   Thumb Code   222  ddiv.o(.text)
    __aeabi_ui2d                             0x08000f27   Thumb Code    26  dfltui.o(.text)
    __aeabi_f2d                              0x08000f41   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08000f67   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08000f9f   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08000f9f   Thumb Code    44  uidiv.o(.text)
    __aeabi_llsl                             0x08000fcb   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x08000fcb   Thumb Code     0  llshl.o(.text)
    __aeabi_llsr                             0x08000fe9   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x08000fe9   Thumb Code     0  llushr.o(.text)
    __aeabi_lasr                             0x08001009   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x08001009   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x0800102d   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x0800102d   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x0800103f   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800109b   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x080010b9   Thumb Code   156  depilogue.o(.text)
    __scatterload                            0x08001155   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08001155   Thumb Code     0  init.o(.text)
    __aeabi_i2d                              0x08001179   Thumb Code    34  dflti.o(.text)
    __aeabi_d2iz                             0x0800119b   Thumb Code    62  dfixi.o(.text)
    ADC_Cmd                                  0x080011d9   Thumb Code    24  stm32f4xx_adc.o(i.ADC_Cmd)
    ADC_CommonInit                           0x080011f1   Thumb Code    30  stm32f4xx_adc.o(i.ADC_CommonInit)
    ADC_DMACmd                               0x08001219   Thumb Code    24  stm32f4xx_adc.o(i.ADC_DMACmd)
    ADC_DMARequestAfterLastTransferCmd       0x08001231   Thumb Code    24  stm32f4xx_adc.o(i.ADC_DMARequestAfterLastTransferCmd)
    ADC_DiscModeChannelCountConfig           0x08001249   Thumb Code    16  stm32f4xx_adc.o(i.ADC_DiscModeChannelCountConfig)
    ADC_DiscModeCmd                          0x08001259   Thumb Code    24  stm32f4xx_adc.o(i.ADC_DiscModeCmd)
    ADC_Init                                 0x08001271   Thumb Code    66  stm32f4xx_adc.o(i.ADC_Init)
    ADC_RegularChannelConfig                 0x080012bd   Thumb Code   116  stm32f4xx_adc.o(i.ADC_RegularChannelConfig)
    Adc2_Init                                0x08001331   Thumb Code   178  adc.o(i.Adc2_Init)
    Adc3_Init                                0x080013ed   Thumb Code   178  adc.o(i.Adc3_Init)
    Adc_Init                                 0x080014a9   Thumb Code   176  adc.o(i.Adc_Init)
    BusFault_Handler                         0x08001561   Thumb Code     2  stm32f4xx_it.o(i.BusFault_Handler)
    DMA1_Init                                0x08001565   Thumb Code   162  adc.o(i.DMA1_Init)
    DMA2_Init                                0x08001619   Thumb Code   168  adc.o(i.DMA2_Init)
    DMA2_Stream0_IRQHandler                  0x080016d1   Thumb Code    46  adc.o(i.DMA2_Stream0_IRQHandler)
    DMA2_Stream1_IRQHandler                  0x08001711   Thumb Code    46  adc.o(i.DMA2_Stream1_IRQHandler)
    DMA2_Stream2_IRQHandler                  0x08001751   Thumb Code    46  adc.o(i.DMA2_Stream2_IRQHandler)
    DMA3_Init                                0x08001791   Thumb Code   168  adc.o(i.DMA3_Init)
    DMA_ClearITPendingBit                    0x08001849   Thumb Code    32  stm32f4xx_dma.o(i.DMA_ClearITPendingBit)
    DMA_Cmd                                  0x08001875   Thumb Code    24  stm32f4xx_dma.o(i.DMA_Cmd)
    DMA_DeInit                               0x0800188d   Thumb Code   278  stm32f4xx_dma.o(i.DMA_DeInit)
    DMA_GetCmdStatus                         0x080019b9   Thumb Code    14  stm32f4xx_dma.o(i.DMA_GetCmdStatus)
    DMA_GetITStatus                          0x080019c9   Thumb Code    68  stm32f4xx_dma.o(i.DMA_GetITStatus)
    DMA_ITConfig                             0x08001a1d   Thumb Code    50  stm32f4xx_dma.o(i.DMA_ITConfig)
    DMA_Init                                 0x08001a51   Thumb Code    80  stm32f4xx_dma.o(i.DMA_Init)
    DebugMon_Handler                         0x08001aa5   Thumb Code     2  stm32f4xx_it.o(i.DebugMon_Handler)
    FFT                                      0x08001aa9   Thumb Code   124  fft.o(i.FFT)
    GPIO_Init                                0x08001b3d   Thumb Code   124  stm32f4xx_gpio.o(i.GPIO_Init)
    GPIO_PinAFConfig                         0x08001bb9   Thumb Code    32  stm32f4xx_gpio.o(i.GPIO_PinAFConfig)
    Hanningwindow                            0x08001bd9   Thumb Code    80  fft.o(i.Hanningwindow)
    HardFault_Handler                        0x08001c41   Thumb Code     2  stm32f4xx_it.o(i.HardFault_Handler)
    MemManage_Handler                        0x08001c43   Thumb Code     2  stm32f4xx_it.o(i.MemManage_Handler)
    NMI_Handler                              0x08001c45   Thumb Code     2  stm32f4xx_it.o(i.NMI_Handler)
    NVIC_Init                                0x08001c49   Thumb Code    98  misc.o(i.NVIC_Init)
    NVIC_PriorityGroupConfig                 0x08001cb1   Thumb Code    10  misc.o(i.NVIC_PriorityGroupConfig)
    PendSV_Handler                           0x08001cc5   Thumb Code     2  stm32f4xx_it.o(i.PendSV_Handler)
    QCZ_FFT                                  0x08001cc9   Thumb Code   224  adc.o(i.QCZ_FFT)
    RCC_AHB1PeriphClockCmd                   0x08001dc9   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_AHB1PeriphClockCmd)
    RCC_APB1PeriphClockCmd                   0x08001de5   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB1PeriphClockCmd)
    RCC_APB2PeriphClockCmd                   0x08001e01   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphClockCmd)
    RCC_APB2PeriphResetCmd                   0x08001e1d   Thumb Code    22  stm32f4xx_rcc.o(i.RCC_APB2PeriphResetCmd)
    RCC_GetClocksFreq                        0x08001e39   Thumb Code   148  stm32f4xx_rcc.o(i.RCC_GetClocksFreq)
    SVC_Handler                              0x08001edd   Thumb Code     2  stm32f4xx_it.o(i.SVC_Handler)
    SysTick_Handler                          0x08001fb5   Thumb Code     4  stm32f4xx_it.o(i.SysTick_Handler)
    SysTick_Init                             0x08001fb9   Thumb Code    52  bsp_systick.o(i.SysTick_Init)
    SystemInit                               0x08001ff9   Thumb Code    74  system_stm32f4xx.o(i.SystemInit)
    TIM3_Int_Init                            0x08002055   Thumb Code    50  timer.o(i.TIM3_Int_Init)
    TIM_Cmd                                  0x0800208d   Thumb Code    24  stm32f4xx_tim.o(i.TIM_Cmd)
    TIM_SelectOutputTrigger                  0x080020a5   Thumb Code    16  stm32f4xx_tim.o(i.TIM_SelectOutputTrigger)
    TIM_TimeBaseInit                         0x080020b5   Thumb Code    96  stm32f4xx_tim.o(i.TIM_TimeBaseInit)
    TimingDelay_Decrement                    0x08002131   Thumb Code    16  bsp_systick.o(i.TimingDelay_Decrement)
    USART1_IRQHandler                        0x08002145   Thumb Code    92  usart.o(i.USART1_IRQHandler)
    USART_Cmd                                0x080021ad   Thumb Code    24  stm32f4xx_usart.o(i.USART_Cmd)
    USART_GetITStatus                        0x080021c5   Thumb Code    64  stm32f4xx_usart.o(i.USART_GetITStatus)
    USART_ITConfig                           0x08002205   Thumb Code    54  stm32f4xx_usart.o(i.USART_ITConfig)
    USART_Init                               0x0800223d   Thumb Code   180  stm32f4xx_usart.o(i.USART_Init)
    USART_ReceiveData                        0x080022f9   Thumb Code     8  stm32f4xx_usart.o(i.USART_ReceiveData)
    UsageFault_Handler                       0x08002301   Thumb Code     2  stm32f4xx_it.o(i.UsageFault_Handler)
    __0printf$1                              0x08002305   Thumb Code    22  printf1.o(i.__0printf$1)
    __1printf$1                              0x08002305   Thumb Code     0  printf1.o(i.__0printf$1)
    __2printf                                0x08002305   Thumb Code     0  printf1.o(i.__0printf$1)
    __ARM_fpclassify                         0x08002325   Thumb Code    48  fpclassify.o(i.__ARM_fpclassify)
    __hardfp_atan                            0x08002359   Thumb Code   622  atan.o(i.__hardfp_atan)
    __hardfp_atan2                           0x08002631   Thumb Code   432  atan2.o(i.__hardfp_atan2)
    __hardfp_cos                             0x08002821   Thumb Code   180  cos.o(i.__hardfp_cos)
    __ieee754_rem_pio2                       0x080028e9   Thumb Code   938  rred.o(i.__ieee754_rem_pio2)
    __kernel_cos                             0x08002d21   Thumb Code   322  cos_i.o(i.__kernel_cos)
    __kernel_poly                            0x08002e91   Thumb Code   248  poly.o(i.__kernel_poly)
    __kernel_sin                             0x08002f89   Thumb Code   280  sin_i.o(i.__kernel_sin)
    __mathlib_dbl_infnan                     0x080030b9   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan)
    __mathlib_dbl_infnan2                    0x080030cd   Thumb Code    20  dunder.o(i.__mathlib_dbl_infnan2)
    __mathlib_dbl_invalid                    0x080030e1   Thumb Code    24  dunder.o(i.__mathlib_dbl_invalid)
    __mathlib_dbl_underflow                  0x08003101   Thumb Code    24  dunder.o(i.__mathlib_dbl_underflow)
    __scatterload_copy                       0x08003121   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x0800312f   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003131   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    __set_errno                              0x08003141   Thumb Code     6  errno.o(i.__set_errno)
    atan                                     0x080032a1   Thumb Code    16  atan.o(i.atan)
    fabs                                     0x080032b1   Thumb Code    24  fabs.o(i.fabs)
    find_peak_indices                        0x080032c9   Thumb Code   124  fft.o(i.find_peak_indices)
    fputc                                    0x08003349   Thumb Code    16  usart.o(i.fputc)
    main                                     0x0800335d   Thumb Code   236  main.o(i.main)
    uart_init                                0x080034a9   Thumb Code   164  usart.o(i.uart_init)
    armBitRevTable                           0x08003554   Data        2048  arm_common_tables.o(.constdata)
    twiddleCoef_4096                         0x08003d54   Data       32768  arm_common_tables.o(.constdata)
    __mathlib_zero                           0x0800be20   Data           8  qnan.o(.constdata)
    Region$$Table$$Base                      0x0800bf18   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x0800bf38   Number         0  anon$$obj.o(Region$$Table)
    SystemCoreClock                          0x20000000   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x20000004   Data          16  system_stm32f4xx.o(.data)
    waveform_A                               0x20000024   Data           2  main.o(.data)
    waveform_B                               0x20000026   Data           2  main.o(.data)
    frequency_A                              0x20000028   Data           4  main.o(.data)
    frequency_B                              0x2000002c   Data           4  main.o(.data)
    USART_RX_STA                             0x20000034   Data           2  usart.o(.data)
    DEBUG_USARTx                             0x20000038   Data           4  usart.o(.data)
    __stdout                                 0x2000003c   Data           4  usart.o(.data)
    flag_ADC                                 0x20000040   Data           1  adc.o(.data)
    flag_ADC1                                0x20000041   Data           1  adc.o(.data)
    flag_ADC2                                0x20000042   Data           1  adc.o(.data)
    phase                                    0x20000044   Data           4  adc.o(.data)
    phase_A                                  0x20000048   Data           4  adc.o(.data)
    phase_B                                  0x2000004c   Data           4  adc.o(.data)
    timef                                    0x20000052   Data           2  fft.o(.data)
    Adresult                                 0x20000054   Data           4  fft.o(.data)
    frequency                                0x20000058   Data           4  fft.o(.data)
    set_rightk                               0x2000005c   Data           4  fft.o(.data)
    length                                   0x20000060   Data           4  fft.o(.data)
    sampfre                                  0x20000064   Data           4  fft.o(.data)
    k                                        0x20000068   Data           4  fft.o(.data)
    peak1_idx                                0x2000006c   Data           4  fft.o(.data)
    peak2_idx                                0x20000070   Data           4  fft.o(.data)
    USART_RX_BUF                             0x20000078   Data         200  usart.o(.bss)
    buff_adc                                 0x20000140   Data        8192  adc.o(.bss)
    buff_adc2                                0x20002140   Data        8192  adc.o(.bss)
    buff_adc3                                0x20004140   Data        8192  adc.o(.bss)
    scfft                                    0x20006140   Data          20  fft.o(.bss)
    fft_inputbuf                             0x20006154   Data       32768  fft.o(.bss)
    fft_outputbuf                            0x2000e154   Data       16384  fft.o(.bss)
    __initial_sp                             0x20012558   Data           0  startup_stm32f429_439xx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x080001ad

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x0000bfb0, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x0000bf38, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x000001ac   Data   RO            3    RESET               startup_stm32f429_439xx.o
    0x080001ac   0x080001ac   0x00000000   Code   RO         6342  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x080001ac   0x080001ac   0x00000004   Code   RO         6673    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x080001b0   0x080001b0   0x00000004   Code   RO         6676    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6678    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x080001b4   0x080001b4   0x00000000   Code   RO         6680    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x080001b4   0x080001b4   0x00000008   Code   RO         6681    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         6683    .ARM.Collect$$$$0000000D  mc_w.l(entry10a.o)
    0x080001bc   0x080001bc   0x00000000   Code   RO         6685    .ARM.Collect$$$$0000000F  mc_w.l(entry11a.o)
    0x080001bc   0x080001bc   0x00000004   Code   RO         6674    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001c0   0x080001c0   0x00000024   Code   RO            4    .text               startup_stm32f429_439xx.o
    0x080001e4   0x080001e4   0x000000fc   Code   RO         6102    .text               arm_cortexM4lf_math.lib(arm_cmplx_mag_f32.o)
    0x080002e0   0x080002e0   0x000006a0   Code   RO         6148    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_f32.o)
    0x08000980   0x08000980   0x000000c0   Code   RO         6175    .text               arm_cortexM4lf_math.lib(arm_cfft_radix4_init_f32.o)
    0x08000a40   0x08000a40   0x000001d6   Code   RO         6203    .text               arm_cortexM4lf_math.lib(arm_bitreversal.o)
    0x08000c16   0x08000c16   0x0000014e   Code   RO         6610    .text               mf_w.l(dadd.o)
    0x08000d64   0x08000d64   0x000000e4   Code   RO         6612    .text               mf_w.l(dmul.o)
    0x08000e48   0x08000e48   0x000000de   Code   RO         6614    .text               mf_w.l(ddiv.o)
    0x08000f26   0x08000f26   0x0000001a   Code   RO         6616    .text               mf_w.l(dfltui.o)
    0x08000f40   0x08000f40   0x00000026   Code   RO         6620    .text               mf_w.l(f2d.o)
    0x08000f66   0x08000f66   0x00000038   Code   RO         6622    .text               mf_w.l(d2f.o)
    0x08000f9e   0x08000f9e   0x0000002c   Code   RO         6687    .text               mc_w.l(uidiv.o)
    0x08000fca   0x08000fca   0x0000001e   Code   RO         6691    .text               mc_w.l(llshl.o)
    0x08000fe8   0x08000fe8   0x00000020   Code   RO         6693    .text               mc_w.l(llushr.o)
    0x08001008   0x08001008   0x00000024   Code   RO         6695    .text               mc_w.l(llsshr.o)
    0x0800102c   0x0800102c   0x00000000   Code   RO         6704    .text               mc_w.l(iusefp.o)
    0x0800102c   0x0800102c   0x0000006e   Code   RO         6705    .text               mf_w.l(fepilogue.o)
    0x0800109a   0x0800109a   0x000000ba   Code   RO         6707    .text               mf_w.l(depilogue.o)
    0x08001154   0x08001154   0x00000024   Code   RO         6719    .text               mc_w.l(init.o)
    0x08001178   0x08001178   0x00000022   Code   RO         6721    .text               mf_w.l(dflti.o)
    0x0800119a   0x0800119a   0x0000003e   Code   RO         6723    .text               mf_w.l(dfixi.o)
    0x080011d8   0x080011d8   0x00000018   Code   RO          222    i.ADC_Cmd           stm32f4xx_adc.o
    0x080011f0   0x080011f0   0x00000028   Code   RO          223    i.ADC_CommonInit    stm32f4xx_adc.o
    0x08001218   0x08001218   0x00000018   Code   RO          226    i.ADC_DMACmd        stm32f4xx_adc.o
    0x08001230   0x08001230   0x00000018   Code   RO          227    i.ADC_DMARequestAfterLastTransferCmd  stm32f4xx_adc.o
    0x08001248   0x08001248   0x00000010   Code   RO          229    i.ADC_DiscModeChannelCountConfig  stm32f4xx_adc.o
    0x08001258   0x08001258   0x00000018   Code   RO          230    i.ADC_DiscModeCmd   stm32f4xx_adc.o
    0x08001270   0x08001270   0x0000004c   Code   RO          242    i.ADC_Init          stm32f4xx_adc.o
    0x080012bc   0x080012bc   0x00000074   Code   RO          247    i.ADC_RegularChannelConfig  stm32f4xx_adc.o
    0x08001330   0x08001330   0x000000bc   Code   RO         5875    i.Adc2_Init         adc.o
    0x080013ec   0x080013ec   0x000000bc   Code   RO         5876    i.Adc3_Init         adc.o
    0x080014a8   0x080014a8   0x000000b8   Code   RO         5877    i.Adc_Init          adc.o
    0x08001560   0x08001560   0x00000002   Code   RO         5275    i.BusFault_Handler  stm32f4xx_it.o
    0x08001562   0x08001562   0x00000002   PAD
    0x08001564   0x08001564   0x000000b4   Code   RO         5878    i.DMA1_Init         adc.o
    0x08001618   0x08001618   0x000000b8   Code   RO         5879    i.DMA2_Init         adc.o
    0x080016d0   0x080016d0   0x00000040   Code   RO         5880    i.DMA2_Stream0_IRQHandler  adc.o
    0x08001710   0x08001710   0x00000040   Code   RO         5881    i.DMA2_Stream1_IRQHandler  adc.o
    0x08001750   0x08001750   0x00000040   Code   RO         5882    i.DMA2_Stream2_IRQHandler  adc.o
    0x08001790   0x08001790   0x000000b8   Code   RO         5883    i.DMA3_Init         adc.o
    0x08001848   0x08001848   0x0000002c   Code   RO         1221    i.DMA_ClearITPendingBit  stm32f4xx_dma.o
    0x08001874   0x08001874   0x00000018   Code   RO         1222    i.DMA_Cmd           stm32f4xx_dma.o
    0x0800188c   0x0800188c   0x0000012c   Code   RO         1223    i.DMA_DeInit        stm32f4xx_dma.o
    0x080019b8   0x080019b8   0x0000000e   Code   RO         1227    i.DMA_GetCmdStatus  stm32f4xx_dma.o
    0x080019c6   0x080019c6   0x00000002   PAD
    0x080019c8   0x080019c8   0x00000054   Code   RO         1232    i.DMA_GetITStatus   stm32f4xx_dma.o
    0x08001a1c   0x08001a1c   0x00000032   Code   RO         1233    i.DMA_ITConfig      stm32f4xx_dma.o
    0x08001a4e   0x08001a4e   0x00000002   PAD
    0x08001a50   0x08001a50   0x00000054   Code   RO         1234    i.DMA_Init          stm32f4xx_dma.o
    0x08001aa4   0x08001aa4   0x00000002   Code   RO         5276    i.DebugMon_Handler  stm32f4xx_it.o
    0x08001aa6   0x08001aa6   0x00000002   PAD
    0x08001aa8   0x08001aa8   0x00000094   Code   RO         6010    i.FFT               fft.o
    0x08001b3c   0x08001b3c   0x0000007c   Code   RO         2046    i.GPIO_Init         stm32f4xx_gpio.o
    0x08001bb8   0x08001bb8   0x00000020   Code   RO         2047    i.GPIO_PinAFConfig  stm32f4xx_gpio.o
    0x08001bd8   0x08001bd8   0x00000068   Code   RO         6014    i.Hanningwindow     fft.o
    0x08001c40   0x08001c40   0x00000002   Code   RO         5277    i.HardFault_Handler  stm32f4xx_it.o
    0x08001c42   0x08001c42   0x00000002   Code   RO         5278    i.MemManage_Handler  stm32f4xx_it.o
    0x08001c44   0x08001c44   0x00000002   Code   RO         5279    i.NMI_Handler       stm32f4xx_it.o
    0x08001c46   0x08001c46   0x00000002   PAD
    0x08001c48   0x08001c48   0x00000068   Code   RO          164    i.NVIC_Init         misc.o
    0x08001cb0   0x08001cb0   0x00000014   Code   RO          165    i.NVIC_PriorityGroupConfig  misc.o
    0x08001cc4   0x08001cc4   0x00000002   Code   RO         5280    i.PendSV_Handler    stm32f4xx_it.o
    0x08001cc6   0x08001cc6   0x00000002   PAD
    0x08001cc8   0x08001cc8   0x00000100   Code   RO         5884    i.QCZ_FFT           adc.o
    0x08001dc8   0x08001dc8   0x0000001c   Code   RO         2960    i.RCC_AHB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001de4   0x08001de4   0x0000001c   Code   RO         2969    i.RCC_APB1PeriphClockCmd  stm32f4xx_rcc.o
    0x08001e00   0x08001e00   0x0000001c   Code   RO         2972    i.RCC_APB2PeriphClockCmd  stm32f4xx_rcc.o
    0x08001e1c   0x08001e1c   0x0000001c   Code   RO         2974    i.RCC_APB2PeriphResetCmd  stm32f4xx_rcc.o
    0x08001e38   0x08001e38   0x000000a4   Code   RO         2981    i.RCC_GetClocksFreq  stm32f4xx_rcc.o
    0x08001edc   0x08001edc   0x00000002   Code   RO         5281    i.SVC_Handler       stm32f4xx_it.o
    0x08001ede   0x08001ede   0x00000002   PAD
    0x08001ee0   0x08001ee0   0x000000d4   Code   RO           13    i.SetSysClock       system_stm32f4xx.o
    0x08001fb4   0x08001fb4   0x00000004   Code   RO         5282    i.SysTick_Handler   stm32f4xx_it.o
    0x08001fb8   0x08001fb8   0x00000040   Code   RO         5375    i.SysTick_Init      bsp_systick.o
    0x08001ff8   0x08001ff8   0x0000005c   Code   RO           15    i.SystemInit        system_stm32f4xx.o
    0x08002054   0x08002054   0x00000038   Code   RO         5976    i.TIM3_Int_Init     timer.o
    0x0800208c   0x0800208c   0x00000018   Code   RO         4353    i.TIM_Cmd           stm32f4xx_tim.o
    0x080020a4   0x080020a4   0x00000010   Code   RO         4411    i.TIM_SelectOutputTrigger  stm32f4xx_tim.o
    0x080020b4   0x080020b4   0x0000007c   Code   RO         4425    i.TIM_TimeBaseInit  stm32f4xx_tim.o
    0x08002130   0x08002130   0x00000014   Code   RO         5376    i.TimingDelay_Decrement  bsp_systick.o
    0x08002144   0x08002144   0x00000068   Code   RO         5763    i.USART1_IRQHandler  usart.o
    0x080021ac   0x080021ac   0x00000018   Code   RO         4907    i.USART_Cmd         stm32f4xx_usart.o
    0x080021c4   0x080021c4   0x00000040   Code   RO         4911    i.USART_GetITStatus  stm32f4xx_usart.o
    0x08002204   0x08002204   0x00000036   Code   RO         4913    i.USART_ITConfig    stm32f4xx_usart.o
    0x0800223a   0x0800223a   0x00000002   PAD
    0x0800223c   0x0800223c   0x000000bc   Code   RO         4914    i.USART_Init        stm32f4xx_usart.o
    0x080022f8   0x080022f8   0x00000008   Code   RO         4921    i.USART_ReceiveData  stm32f4xx_usart.o
    0x08002300   0x08002300   0x00000002   Code   RO         5283    i.UsageFault_Handler  stm32f4xx_it.o
    0x08002302   0x08002302   0x00000002   PAD
    0x08002304   0x08002304   0x00000020   Code   RO         6394    i.__0printf$1       mc_w.l(printf1.o)
    0x08002324   0x08002324   0x00000030   Code   RO         6715    i.__ARM_fpclassify  m_wm.l(fpclassify.o)
    0x08002354   0x08002354   0x00000004   PAD
    0x08002358   0x08002358   0x000002d8   Code   RO         6624    i.__hardfp_atan     m_wm.l(atan.o)
    0x08002630   0x08002630   0x000001f0   Code   RO         6306    i.__hardfp_atan2    m_wm.l(atan2.o)
    0x08002820   0x08002820   0x000000c8   Code   RO         6318    i.__hardfp_cos      m_wm.l(cos.o)
    0x080028e8   0x080028e8   0x00000438   Code   RO         6662    i.__ieee754_rem_pio2  m_wm.l(rred.o)
    0x08002d20   0x08002d20   0x00000170   Code   RO         6638    i.__kernel_cos      m_wm.l(cos_i.o)
    0x08002e90   0x08002e90   0x000000f8   Code   RO         6717    i.__kernel_poly     m_wm.l(poly.o)
    0x08002f88   0x08002f88   0x00000130   Code   RO         6667    i.__kernel_sin      m_wm.l(sin_i.o)
    0x080030b8   0x080030b8   0x00000014   Code   RO         6642    i.__mathlib_dbl_infnan  m_wm.l(dunder.o)
    0x080030cc   0x080030cc   0x00000014   Code   RO         6643    i.__mathlib_dbl_infnan2  m_wm.l(dunder.o)
    0x080030e0   0x080030e0   0x00000020   Code   RO         6644    i.__mathlib_dbl_invalid  m_wm.l(dunder.o)
    0x08003100   0x08003100   0x00000020   Code   RO         6647    i.__mathlib_dbl_underflow  m_wm.l(dunder.o)
    0x08003120   0x08003120   0x0000000e   Code   RO         6727    i.__scatterload_copy  mc_w.l(handlers.o)
    0x0800312e   0x0800312e   0x00000002   Code   RO         6728    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003130   0x08003130   0x0000000e   Code   RO         6729    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x0800313e   0x0800313e   0x00000002   PAD
    0x08003140   0x08003140   0x0000000c   Code   RO         6699    i.__set_errno       mc_w.l(errno.o)
    0x0800314c   0x0800314c   0x00000154   Code   RO         6401    i._printf_core      mc_w.l(printf1.o)
    0x080032a0   0x080032a0   0x00000010   Code   RO         6626    i.atan              m_wm.l(atan.o)
    0x080032b0   0x080032b0   0x00000018   Code   RO         6657    i.fabs              m_wm.l(fabs.o)
    0x080032c8   0x080032c8   0x00000080   Code   RO         6015    i.find_peak_indices  fft.o
    0x08003348   0x08003348   0x00000014   Code   RO         5765    i.fputc             usart.o
    0x0800335c   0x0800335c   0x0000014c   Code   RO         5161    i.main              main.o
    0x080034a8   0x080034a8   0x000000ac   Code   RO         5766    i.uart_init         usart.o
    0x08003554   0x08003554   0x00000800   Data   RO         6227    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x08003d54   0x08003d54   0x00008000   Data   RO         6236    .constdata          arm_cortexM4lf_math.lib(arm_common_tables.o)
    0x0800bd54   0x0800bd54   0x00000004   PAD
    0x0800bd58   0x0800bd58   0x00000098   Data   RO         6627    .constdata          m_wm.l(atan.o)
    0x0800bdf0   0x0800bdf0   0x00000030   Data   RO         6639    .constdata          m_wm.l(cos_i.o)
    0x0800be20   0x0800be20   0x00000008   Data   RO         6661    .constdata          m_wm.l(qnan.o)
    0x0800be28   0x0800be28   0x000000c8   Data   RO         6664    .constdata          m_wm.l(rred.o)
    0x0800bef0   0x0800bef0   0x00000028   Data   RO         6668    .constdata          m_wm.l(sin_i.o)
    0x0800bf18   0x0800bf18   0x00000020   Data   RO         6725    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x0800bf38, Size: 0x00012558, Max: 0x00030000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x0800bf38   0x00000014   Data   RW           16    .data               system_stm32f4xx.o
    0x20000014   0x0800bf4c   0x00000010   Data   RW         3013    .data               stm32f4xx_rcc.o
    0x20000024   0x0800bf5c   0x0000000c   Data   RW         5183    .data               main.o
    0x20000030   0x0800bf68   0x00000004   Data   RW         5377    .data               bsp_systick.o
    0x20000034   0x0800bf6c   0x00000008   Data   RW         5768    .data               usart.o
    0x2000003c   0x0800bf74   0x00000004   Data   RW         5769    .data               usart.o
    0x20000040   0x0800bf78   0x00000010   Data   RW         5887    .data               adc.o
    0x20000050   0x0800bf88   0x00000024   Data   RW         6027    .data               fft.o
    0x20000074   0x0800bfac   0x00000004   Data   RW         6700    .data               mc_w.l(errno.o)
    0x20000078        -       0x000000c8   Zero   RW         5767    .bss                usart.o
    0x20000140        -       0x00006000   Zero   RW         5886    .bss                adc.o
    0x20006140        -       0x0000c014   Zero   RW         6022    .bss                fft.o
    0x20012154   0x0800bfb0   0x00000004   PAD
    0x20012158        -       0x00000400   Zero   RW            1    STACK               startup_stm32f429_439xx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

      1556        164          0         16      24576       8312   adc.o
        84         16          0          4          0      28402   bsp_systick.o
       380         52          0         36      49172       4474   fft.o
       332         96          0         12          0      16836   main.o
       124         16          0          0          0       1979   misc.o
        36          8        428          0       1024       1024   startup_stm32f429_439xx.o
       344         20          0          0          0       7650   stm32f4xx_adc.o
       600         54          0          0          0       7126   stm32f4xx_dma.o
       156          0          0          0          0       2171   stm32f4xx_gpio.o
        20          0          0          0          0       4726   stm32f4xx_it.o
       276         40          0         16          0       6292   stm32f4xx_rcc.o
       164         28          0          0          0       3019   stm32f4xx_tim.o
       338          8          0          0          0       5330   stm32f4xx_usart.o
       304         34          0         20          0     323713   system_stm32f4xx.o
        56          6          0          0          0        700   timer.o
       296         24          0         12        200       4153   usart.o

    ----------------------------------------------------------------------
      5084        <USER>        <GROUP>        116      74976     425907   Object Totals
         0          0         32          0          0          0   (incl. Generated)
        18          0          0          0          4          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

       470          0          0          0          0       3046   arm_bitreversal.o
      1696          0          0          0          0      18824   arm_cfft_radix4_f32.o
       192         46          0          0          0        947   arm_cfft_radix4_init_f32.o
       252          6          0          0          0      17652   arm_cmplx_mag_f32.o
         0          0      34816          0          0       4704   arm_common_tables.o
       744        106        152          0          0        352   atan.o
       496         64          0          0          0        192   atan2.o
       200         20          0          0          0        164   cos.o
       368         46         48          0          0        200   cos_i.o
       104         16          0          0          0        496   dunder.o
        24          0          0          0          0        124   fabs.o
        48          0          0          0          0        124   fpclassify.o
       248          0          0          0          0        152   poly.o
         0          0          8          0          0          0   qnan.o
      1080        142        200          0          0        188   rred.o
       304         24         40          0          0        208   sin_i.o
         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        12          6          0          4          0         68   errno.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
       372         14          0          0          0        184   printf1.o
        44          0          0          0          0         80   uidiv.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        62          0          0          0          0         80   dfixi.o
        34          0          0          0          0         76   dflti.o
        26          0          0          0          0         76   dfltui.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      8140        <USER>      <GROUP>          4          0      49053   Library Totals
         6          0          4          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2610         52      34816          0          0      45173   arm_cortexM4lf_math.lib
      3616        418        448          0          0       2200   m_wm.l
       612         36          0          4          0        604   mc_w.l
      1296          0          0          0          0       1076   mf_w.l

    ----------------------------------------------------------------------
      8140        <USER>      <GROUP>          4          0      49053   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     13224       1072      35728        120      74976     466984   Grand Totals
     13224       1072      35728        120      74976     466984   ELF Image Totals
     13224       1072      35728        120          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                48952 (  47.80kB)
    Total RW  Size (RW Data + ZI Data)             75096 (  73.34kB)
    Total ROM Size (Code + RO Data + RW Data)      49072 (  47.92kB)

==============================================================================

