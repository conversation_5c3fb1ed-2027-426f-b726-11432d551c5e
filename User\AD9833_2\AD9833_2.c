/**********************************************************
                       康威电子
功能：stm32f429控制第二个AD9833模块
接口：控制引脚接口请参照AD9833_2.h
时间：2025/07/08
版本：1.0
作者：基于原版AD9833驱动修改
其他：本程序只供学习使用，盗版必究。

更多电子需求，请到淘宝店，康威电子竭诚为您服务 ^_^
https://kvdz.taobao.com/ 
**********************************************************/
#include "AD9833_2.h"		
#include "./systick/bsp_SysTick.h"

//时钟速率为25 MHz时， 可以实现0.1 Hz的分辨率；而时钟速率为1 MHz时，则可以实现0.004 Hz的分辨率。
//调整参考时钟修改此处即可。
#define FCLK_2 25000000	//设置参考时钟25MHz，板默认板载晶振频率25Mhz。
#define AD9833_2_FSYNC 	PFout(6)
#define AD9833_2_SCLK 	PFout(7)  // 如果不共享SCLK可以用这个
//#define AD9833_2_SCLK   PAout(4)   // 共享第一个AD9833的SCLK
#define AD9833_2_SDATA 	PFout(8)

#define RealFreDat_2    268435456.0/FCLK_2//总的公式为 Fout=（Fclk/2的28次方）*28位寄存器的值

/************************************************************
** 函数名称 ：void AD983_2_GPIO_Init(void)  
** 函数功能 ：初始化控制第二个AD9833需要用到的IO口
** 入口参数 ：无
** 出口参数 ：无
** 函数说明 ：无
**************************************************************/

void AD983_2_GPIO_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure ;
	
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOF, ENABLE);	 //使能PF端口时钟 F429

    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6| GPIO_Pin_7| GPIO_Pin_8;
//	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_6| GPIO_Pin_8;  //PF6(FSYNC), PF8(SDATA)
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_OUT;        //输出模式 F429
    GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;       //推挽输出 F429
//    GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL;    //无上下拉 F429
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz ;

    GPIO_Init(GPIOF ,&GPIO_InitStructure) ;
}

/**********************************************************************************************
** 函数名称 ：unsigned char AD9833_2_SPI_Write(unsigned char* data,unsigned char bytesNumber)
** 函数功能 ：使用模拟SPI向第二个AD9833写数据
** 入口参数 ：* data:写入数据缓冲区,第一个字节是寄存器地址；第二个字节开始要写入的数据。
						bytesNumber: 要写入的字节数
** 出口参数 ：无
** 函数说明 ：无
************************************************************************************************/
unsigned char AD9833_2_SPI_Write(unsigned char* data,unsigned char bytesNumber)
{
  unsigned char i,j; 
	unsigned char writeData[5]	= {0,0, 0, 0, 0};
	
    AD9833_2_SCLK=1; 
    AD9833_2_FSYNC=0 ; 

	for(i = 0;i < bytesNumber;i ++)
	{
		writeData[i] = data[i + 1];
	}
	
	for(i=0 ;i<bytesNumber ;i++) 
	{
    for(j=0 ;j<8 ;j++)      
		{ 
        if(writeData[i] & 0x80) 
          AD9833_2_SDATA=1; 
        else 
          AD9833_2_SDATA=0; 

        AD9833_2_SCLK=0; 
        writeData[i] <<= 1; 
        AD9833_2_SCLK=1; 
    } 
	}
    AD9833_2_SDATA=1; 
    AD9833_2_FSYNC=1; 
		
		return i;
}

/************************************************************
** 函数名称 ：void AD9833_2_Init(void)  
** 函数功能 ：初始化控制第二个AD9833需要用到的IO口及寄存器
** 入口参数 ：无
** 出口参数 ：无
** 函数说明 ：无
**************************************************************/
void AD9833_2_Init(void)
{
    AD983_2_GPIO_Init();
    AD9833_2_SetRegisterValue(AD9833_2_REG_CMD | AD9833_2_RESET);
}

void Delay_2(__IO uint32_t nCount)	 //简单的延时函数
{
	for(; nCount != 0; nCount--);
}
/*****************************************************************************************
** 函数名称 ：void AD9833_2_Reset(void)  
** 函数功能 ：设置第二个AD9833的复位位
** 入口参数 ：无
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_Reset(void)
{
	AD9833_2_SetRegisterValue(AD9833_2_REG_CMD | AD9833_2_RESET);
//	delay_ms(10);
//	Delay(600000);
	Delay_ms(10);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_ClearReset(void)  
** 函数功能 ：清除第二个AD9833的复位位。
** 入口参数 ：无
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_ClearReset(void)
{
	AD9833_2_SetRegisterValue(AD9833_2_REG_CMD);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_SetRegisterValue(unsigned short regValue)
** 函数功能 ：将值写入第二个AD9833寄存器
** 入口参数 ：regValue：要写入寄存器的值。
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_SetRegisterValue(unsigned short regValue)
{
	unsigned char data[5] = {0x03, 0x00, 0x00};

	data[1] = (unsigned char)((regValue & 0xFF00) >> 8);
	data[2] = (unsigned char)((regValue & 0x00FF) >> 0);
	AD9833_2_SPI_Write(data,2);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_SetFrequencyQuick(float fout,unsigned short type)
** 函数功能 ：写入第二个AD9833频率寄存器
** 入口参数 ：val：要写入的频率值。
**						type：波形类型；AD9833_2_OUT_SINUS正弦波、AD9833_2_OUT_TRIANGLE三角波、AD9833_2_OUT_MSB方波
** 出口参数 ：无
** 函数说明 ：时钟速率为25 MHz时， 可以实现0.1 Hz的分辨率；而时钟速率为1 MHz时，则可以实现0.004 Hz的分辨率。
*******************************************************************************************/
void AD9833_2_SetFrequencyQuick(float fout,unsigned short type)
{
	AD9833_2_SetFrequency(AD9833_2_REG_FREQ0, fout,type);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_SetFrequency(unsigned short reg, float fout,unsigned short type)
** 函数功能 ：写入第二个AD9833频率寄存器
** 入口参数 ：reg：要写入的频率寄存器。
**						val：要写入的值。
**						type：波形类型；AD9833_2_OUT_SINUS正弦波、AD9833_2_OUT_TRIANGLE三角波、AD9833_2_OUT_MSB方波
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_SetFrequency(unsigned short reg, float fout,unsigned short type)
{
	unsigned short freqHi = reg;
	unsigned short freqLo = reg;
	unsigned long val=RealFreDat_2*fout;
	freqHi |= (val & 0xFFFC000) >> 14 ;
	freqLo |= (val & 0x3FFF);
	AD9833_2_SetRegisterValue(AD9833_2_B28|type);
	AD9833_2_SetRegisterValue(freqLo);
	AD9833_2_SetRegisterValue(freqHi);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_SetPhase(unsigned short reg, unsigned short val)
** 函数功能 ：写入第二个AD9833相位寄存器。
** 入口参数 ：reg：要写入的相位寄存器。
**						val：要写入的值。
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_SetPhase(unsigned short reg, unsigned short val)
{
	unsigned short phase = reg;
	phase |= val;
	AD9833_2_SetRegisterValue(phase);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_Setup(unsigned short freq, unsigned short phase,unsigned short type)
** 函数功能 ：写入第二个AD9833相位寄存器。
** 入口参数 ：freq：使用的频率寄存器。
							phase：使用的相位寄存器。
							type：要输出的波形类型。
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_Setup(unsigned short freq, unsigned short phase,unsigned short type)
{
	unsigned short val = 0;

	val = freq | phase | type;
	AD9833_2_SetRegisterValue(val);
}

/*****************************************************************************************
** 函数名称 ：void AD9833_2_SetWave(unsigned short type)
** 函数功能 ：设置第二个AD9833要输出的波形类型。
** 入口参数 ：type：要输出的波形类型。
** 出口参数 ：无
** 函数说明 ：无
*******************************************************************************************/
void AD9833_2_SetWave(unsigned short type)
{
	AD9833_2_SetRegisterValue(type);
}
