#ifndef _AD9833_2_H_
#define _AD9833_2_H_

//#include "sys.h"
#include <stm32f4xx.h>
#include "./systick/bsp_SysTick.h"
#include "stm32f429_winner.h"

//#define BITBAND(addr, bitnum)			((addr & 0xF0000000)+0x2000000+((addr &0xFFFFF)<<5)+(bitnum<<2)) 
//#define MEM_ADDR(addr)					*((volatile unsigned long  *)(addr)) 
//#define BIT_ADDR(addr, bitnum)			MEM_ADDR(BITBAND(addr, bitnum))

//#define GPIOB_ODR_Addr    (GPIOB_BASE+0x14)	//0x40020414 F429
//#define PBout(n)   BIT_ADDR(GPIOB_ODR_Addr,n)  //??? 

#define AD9833_2_FSYNC 	PFout(6)
#define AD9833_2_SCLK 	PFout(7)  // 如果不共享SCLK可以用这个
#define AD9833_2_SDATA 	PFout(8)
//#define AD9833_2_SCLK   PAout(4)   // 共享第一个AD9833的SCLK

/******************************************************************************/
/* AD9833_2                                                                   */
/******************************************************************************/
/* 寄存器 */

#define AD9833_2_REG_CMD		(0 << 14)
#define AD9833_2_REG_FREQ0	(1 << 14)
#define AD9833_2_REG_FREQ1	(2 << 14)
#define AD9833_2_REG_PHASE0	(6 << 13)
#define AD9833_2_REG_PHASE1	(7 << 13)

/* 命令控制位 */

#define AD9833_2_B28				(1 << 13)
#define AD9833_2_HLB				(1 << 12)
#define AD9833_2_FSEL0			(0 << 11)
#define AD9833_2_FSEL1			(1 << 11)
#define AD9833_2_PSEL0			(0 << 10)
#define AD9833_2_PSEL1			(1 << 10)
#define AD9833_2_PIN_SW			(1 << 9)
#define AD9833_2_RESET			(1 << 8)
#define AD9833_2_SLEEP1			(1 << 7)
#define AD9833_2_SLEEP12		(1 << 6)
#define AD9833_2_OPBITEN		(1 << 5)
#define AD9833_2_SIGN_PIB		(1 << 4)
#define AD9833_2_DIV2				(1 << 3)
#define AD9833_2_MODE				(1 << 1)

#define AD9833_2_OUT_SINUS		((0 << 5) | (0 << 1) | (0 << 3))//正弦波 
#define AD9833_2_OUT_TRIANGLE	((0 << 5) | (1 << 1) | (0 << 3))//三角波
#define AD9833_2_OUT_MSB			((1 << 5) | (0 << 1) | (1 << 3)) //方波
#define AD9833_2_OUT_MSB2			((1 << 5) | (0 << 1) | (0 << 3))

void AD983_2_GPIO_Init(void);//初始化IO口
void AD9833_2_Init(void);//初始化IO口及寄存器

void AD9833_2_Reset(void);			//复位AD9833的复位位
void AD9833_2_ClearReset(void);	//清除AD9833的复位位

void AD9833_2_SetRegisterValue(unsigned short regValue);												//将值写入寄存器
void AD9833_2_SetFrequency(unsigned short reg, float fout,unsigned short type);	//写入频率寄存器
void AD9833_2_SetPhase(unsigned short reg, unsigned short val);									//写入相位寄存器

void AD9833_2_Setup(unsigned short freq,unsigned short phase,unsigned short type);//选择频率、相位和波形输出
void AD9833_2_SetFrequencyQuick(float fout,unsigned short type);//快速频率及波形设置
void AD9833_2_SetWave(unsigned short type);//设置波形类型

#endif
